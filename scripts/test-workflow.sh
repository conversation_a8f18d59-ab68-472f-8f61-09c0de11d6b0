#!/bin/bash

# Facebook Automation Desktop - Test Complete Workflow
# This script tests the complete profile management workflow

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# API base URL
API_BASE="http://127.0.0.1:8000/api"

echo "🧪 Testing Facebook Automation Desktop Workflow..."

# Test 1: Health Check
print_info "Testing backend health..."
if curl -s "$API_BASE/../health" > /dev/null 2>&1; then
    print_success "Backend is healthy"
else
    print_error "Backend is not running"
    exit 1
fi

# Test 2: List profiles (should be empty initially)
print_info "Testing profile listing..."
PROFILES=$(curl -s "$API_BASE/profiles/")
if echo "$PROFILES" | grep -q '\[\]'; then
    print_success "Profile listing works (empty list)"
else
    print_success "Profile listing works (existing profiles found)"
fi

# Test 3: Create a test profile
print_info "Testing profile creation..."
CREATE_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Test Workflow Profile",
        "proxy_type": "http",
        "proxy_host": "127.0.0.1",
        "proxy_port": 8080,
        "proxy_username": "testuser",
        "proxy_password": "testpass"
    }')

if echo "$CREATE_RESPONSE" | grep -q '"id"'; then
    PROFILE_ID=$(echo "$CREATE_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    print_success "Profile created successfully (ID: $PROFILE_ID)"
else
    print_error "Failed to create profile"
    echo "Response: $CREATE_RESPONSE"
    exit 1
fi

# Test 4: Get the created profile
print_info "Testing profile retrieval..."
PROFILE_RESPONSE=$(curl -s "$API_BASE/profiles/$PROFILE_ID")
if echo "$PROFILE_RESPONSE" | grep -q '"name":"Test Workflow Profile"'; then
    print_success "Profile retrieved successfully"
else
    print_error "Failed to retrieve profile"
    exit 1
fi

# Test 5: Update the profile
print_info "Testing profile update..."
UPDATE_RESPONSE=$(curl -s -X PUT "$API_BASE/profiles/$PROFILE_ID" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Updated Test Profile",
        "proxy_type": "socks5",
        "proxy_host": "***********",
        "proxy_port": 1080
    }')

if echo "$UPDATE_RESPONSE" | grep -q '"name":"Updated Test Profile"'; then
    print_success "Profile updated successfully"
else
    print_error "Failed to update profile"
    exit 1
fi

# Test 6: Test browser automation endpoints
print_info "Testing browser automation endpoints..."

# Launch browser
print_info "Testing browser launch..."
LAUNCH_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/launch-browser?headless=true")
if echo "$LAUNCH_RESPONSE" | grep -q '"success":true'; then
    print_success "Browser launched successfully"
else
    print_warning "Browser launch may have issues (this is expected without proper proxy)"
    echo "Response: $LAUNCH_RESPONSE"
fi

# Test Facebook endpoints (these will likely fail without proper setup, but we test the API)
print_info "Testing Facebook automation endpoints..."
FB_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/open-facebook")
if echo "$FB_RESPONSE" | grep -q '"success"'; then
    print_success "Facebook automation endpoint accessible"
else
    print_warning "Facebook automation endpoint may have issues (expected without browser)"
fi

# Close browser
print_info "Testing browser close..."
CLOSE_RESPONSE=$(curl -s -X POST "$API_BASE/profiles/$PROFILE_ID/close-browser")
if echo "$CLOSE_RESPONSE" | grep -q '"success"'; then
    print_success "Browser close endpoint works"
else
    print_warning "Browser close may have issues (expected if browser wasn't launched)"
fi

# Test 7: Delete the profile
print_info "Testing profile deletion..."
DELETE_RESPONSE=$(curl -s -X DELETE "$API_BASE/profiles/$PROFILE_ID")
if echo "$DELETE_RESPONSE" | grep -q '"message":"Profile deleted successfully"'; then
    print_success "Profile deleted successfully"
else
    print_error "Failed to delete profile"
    echo "Response: $DELETE_RESPONSE"
    exit 1
fi

# Test 8: Verify profile is deleted
print_info "Verifying profile deletion..."
VERIFY_RESPONSE=$(curl -s "$API_BASE/profiles/$PROFILE_ID")
if echo "$VERIFY_RESPONSE" | grep -q '"detail":"Profile not found"'; then
    print_success "Profile deletion verified"
else
    print_error "Profile still exists after deletion"
    exit 1
fi

print_success "🎉 All workflow tests passed!"
echo ""
print_info "Workflow Summary:"
print_info "✅ Backend health check"
print_info "✅ Profile listing"
print_info "✅ Profile creation"
print_info "✅ Profile retrieval"
print_info "✅ Profile update"
print_info "✅ Browser automation endpoints"
print_info "✅ Profile deletion"
echo ""
print_info "The complete CRUD workflow is working correctly!"
print_info "You can now use the web interface at: http://localhost:3000"
