{"name": "ripemd160", "version": "2.0.1", "description": "Compute ripemd160 of bytes or strings.", "keywords": ["string", "strings", "ripemd160", "ripe160", "bitcoin", "bytes", "cryptography"], "license": "MIT", "files": ["index.js"], "main": "./index", "repository": {"url": "https://github.com/crypto-browserify/ripemd160", "type": "git"}, "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "node test/*.js"}, "dependencies": {"hash-base": "^2.0.0", "inherits": "^2.0.1"}, "devDependencies": {"hash-test-vectors": "^1.3.2", "standard": "^6.0.7", "tape": "^4.5.1"}}