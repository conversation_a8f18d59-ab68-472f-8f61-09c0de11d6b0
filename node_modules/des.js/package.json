{"name": "des.js", "version": "1.1.0", "description": "DES implementation", "main": "lib/des.js", "scripts": {"test": "NODE_OPTIONS=--openssl-legacy-provider mocha --reporter=spec test/*-test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/indutny/des.js.git"}, "keywords": ["DES", "3DES", "EDE", "CBC"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/indutny/des.js/issues"}, "homepage": "https://github.com/indutny/des.js#readme", "devDependencies": {"jshint": "^2.8.0", "mocha": "^10.2.0"}, "dependencies": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}