/// <reference types="./draft-07.ts" />
// @generated
// deno-fmt-ignore-file
// deno-lint-ignore-file
// This code was bundled using `deno bundle` and it's not recommended to edit it manually

const draft = "7";
const $schema = "https://json-schema.org/draft-07/schema";
var ContentEncoding;
(function(ContentEncoding1) {
    ContentEncoding1["7bit"] = "7bit";
    ContentEncoding1["8bit"] = "8bit";
    ContentEncoding1["Base64"] = "base64";
    ContentEncoding1["Binary"] = "binary";
    ContentEncoding1["IETFToken"] = "ietf-token";
    ContentEncoding1["QuotedPrintable"] = "quoted-printable";
    ContentEncoding1["XToken"] = "x-token";
})(ContentEncoding || (ContentEncoding = {}));
var Format;
(function(Format1) {
    Format1["Date"] = "date";
    Format1["DateTime"] = "date-time";
    Format1["Email"] = "email";
    Format1["Hostname"] = "hostname";
    Format1["IDNEmail"] = "idn-email";
    Format1["IDNHostname"] = "idn-hostname";
    Format1["IPv4"] = "ipv4";
    Format1["IPv6"] = "ipv6";
    Format1["IRI"] = "iri";
    Format1["IRIReference"] = "iri-reference";
    Format1["JSONPointer"] = "json-pointer";
    Format1["JSONPointerURIFragment"] = "json-pointer-uri-fragment";
    Format1["RegEx"] = "regex";
    Format1["RelativeJSONPointer"] = "relative-json-pointer";
    Format1["Time"] = "time";
    Format1["URI"] = "uri";
    Format1["URIReference"] = "uri-reference";
    Format1["URITemplate"] = "uri-template";
    Format1["UUID"] = "uuid";
})(Format || (Format = {}));
var TypeName;
(function(TypeName1) {
    TypeName1["Array"] = "array";
    TypeName1["Boolean"] = "boolean";
    TypeName1["Integer"] = "integer";
    TypeName1["Null"] = "null";
    TypeName1["Number"] = "number";
    TypeName1["Object"] = "object";
    TypeName1["String"] = "string";
})(TypeName || (TypeName = {}));
const keywords = [
    "$comment",
    "$id",
    "$ref",
    "$schema",
    "additionalItems",
    "additionalProperties",
    "allOf",
    "anyOf",
    "const",
    "contains",
    "contentEncoding",
    "contentMediaType",
    "default",
    "definitions",
    "dependencies",
    "description",
    "else",
    "enum",
    "examples",
    "exclusiveMaximum",
    "exclusiveMinimum",
    "format",
    "if",
    "items",
    "maximum",
    "maxItems",
    "maxLength",
    "maxProperties",
    "minimum",
    "minItems",
    "minLength",
    "minProperties",
    "multipleOf",
    "not",
    "oneOf",
    "pattern",
    "patternProperties",
    "properties",
    "propertyNames",
    "readOnly",
    "required",
    "then",
    "title",
    "type",
    "uniqueItems",
    "writeOnly", 
];
export { draft as draft };
export { $schema as $schema };
export { ContentEncoding as ContentEncoding };
export { Format as Format };
export { TypeName as TypeName };
export { keywords as keywords };

//# sourceMappingURL=draft-07.js.map