# browserify-rsa

[![NPM Package](https://img.shields.io/npm/v/browserify-rsa.svg?style=flat-square)](https://www.npmjs.org/package/browserify-rsa)
[![Build Status](https://img.shields.io/travis/crypto-browserify/browserify-rsa.svg?branch=master&style=flat-square)](https://travis-ci.org/crypto-browserify/browserify-rsa)
[![Dependency status](https://img.shields.io/david/crypto-browserify/browserify-rsa.svg?style=flat-square)](https://david-dm.org/crypto-browserify/browserify-rsa#info=dependencies)

RSA private decryption/signing using chinese remainder and blinding.

## API

Give it a message as a Buffer and a private key (as decoded by `ASN.1`) and it returns encrypted data as a Buffer.

## LICENSE

MIT
