{"name": "browserify-des", "version": "1.0.2", "description": "", "main": "index.js", "scripts": {"test": "standard && node test.js | tspec"}, "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/browserify-des.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/browserify-des/issues"}, "homepage": "https://github.com/crypto-browserify/browserify-des#readme", "dependencies": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}, "devDependencies": {"standard": "^5.3.1", "tap-spec": "^4.1.0", "tape": "^4.2.0"}}