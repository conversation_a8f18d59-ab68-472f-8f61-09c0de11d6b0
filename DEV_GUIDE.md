# Facebook Automation Desktop - Development Guide

## Development Modes

### 1. Electron App (Desktop)
Chạy ứng dụng như một desktop app với Electron:

```bash
# Chạy production build
./scripts/run-production.sh

# Hoặc chạy từ frontend directory
cd frontend
npm start
```

### 2. Web Browser (Recommended for Development)
Chạy ứng dụng trên web browser để dễ debug:

```bash
# Chạy cả backend và frontend
./scripts/dev-web.sh

# Hoặc chỉ chạy frontend (khi backend đã chạy)
./scripts/frontend-web.sh
```

**URLs khi chạy web mode:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## Development Workflow

### Setup lần đầu
```bash
# 1. Setup project
./scripts/setup-local.sh

# 2. Activate backend environment
cd backend
source venv/bin/activate

# 3. Install frontend dependencies
cd ../frontend
npm install
```

### Development thường ngày

**Option 1: Web Development (Recommended)**
```bash
# Terminal 1: Start everything
./scripts/dev-web.sh
```

**Option 2: Separate terminals**
```bash
# Terminal 1: Backend
cd backend
source venv/bin/activate
python -m uvicorn main:app --reload --port 8000

# Terminal 2: Frontend Web
./scripts/frontend-web.sh
```

**Option 3: Electron Development**
```bash
# Terminal 1: Backend
cd backend
source venv/bin/activate
python -m uvicorn main:app --reload --port 8000

# Terminal 2: Electron App
cd frontend
npm start
```

## Features

### Profile Management
- ✅ Create antidetect browser profiles
- ✅ Configure proxy settings (HTTP, HTTPS, SOCKS5)
- ✅ Generate unique browser fingerprints
- ✅ Launch browser with profile
- ✅ Open Facebook login page
- ✅ Complete Facebook login and save cookies
- ✅ Close browser

### Browser Automation
- ✅ Playwright-based browser automation
- ✅ Antidetect fingerprinting
- ✅ Proxy support
- ✅ Cookie persistence
- ✅ Manual login workflow

### API Endpoints
- `GET /api/profiles/` - List all profiles
- `POST /api/profiles/` - Create new profile
- `PUT /api/profiles/{id}` - Update profile
- `DELETE /api/profiles/{id}` - Delete profile
- `POST /api/profiles/{id}/launch-browser` - Launch browser
- `POST /api/profiles/{id}/open-facebook` - Open Facebook login
- `POST /api/profiles/{id}/complete-login` - Complete login
- `POST /api/profiles/{id}/close-browser` - Close browser

## Debugging

### Web Browser Debugging
1. Chạy `./scripts/dev-web.sh`
2. Mở http://localhost:3000
3. Sử dụng Chrome DevTools để debug
4. Network tab để xem API calls
5. Console tab để xem logs

### Backend Debugging
1. Backend logs hiển thị trong terminal
2. API docs tại http://localhost:8000/docs
3. Database file: `backend/facebook_automation.db`

### Common Issues

**Backend không start:**
```bash
cd backend
source venv/bin/activate
pip install -r requirements.txt
```

**Frontend build errors:**
```bash
cd frontend
npm install
npm run webpack:build
```

**Proxy errors:**
- Kiểm tra proxy settings trong profile
- Test proxy connection trước khi sử dụng

## Project Structure

```
antidetect-browser/
├── backend/                 # Python FastAPI backend
│   ├── app/                # Application code
│   ├── venv/               # Python virtual environment
│   └── main.py             # Entry point
├── frontend/               # React frontend
│   ├── src/                # Source code
│   ├── dist/               # Built files
│   └── package.json        # Dependencies
├── scripts/                # Development scripts
│   ├── dev-web.sh          # Web development
│   ├── frontend-web.sh     # Frontend only
│   └── run-production.sh   # Production mode
└── crawl4ai/               # Browser automation library
```
