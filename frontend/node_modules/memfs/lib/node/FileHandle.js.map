{"version": 3, "file": "FileHandle.js", "sourceRoot": "", "sources": ["../../src/node/FileHandle.ts"], "names": [], "mappings": ";;;AAAA,iCAAmC;AAKnC,MAAa,UAAU;IAKrB,YAAY,EAAiB,EAAE,EAAU;QACvC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACf,CAAC;IAED,UAAU,CAAC,IAAW,EAAE,OAA0C;QAChE,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAClE,CAAC;IAED,KAAK,CAAC,IAAW;QACf,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,GAAW,EAAE,GAAW;QAC5B,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACzD,CAAC;IAED,KAAK;QACH,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,QAAQ;QACN,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,gBAAgB,CAAC,OAA0C;QACzD,OAAO,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE,kCAAO,OAAO,KAAE,EAAE,EAAE,IAAI,IAAG,CAAC;IAChE,CAAC;IAED,iBAAiB,CAAC,OAA2C;QAC3D,OAAO,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,EAAE,kCAAO,OAAO,KAAE,EAAE,EAAE,IAAI,IAAG,CAAC;IACjE,CAAC;IAED,iBAAiB,CAAC,OAAwC;QACxD,OAAO,IAAI,cAAc,CAAC;YACxB,IAAI,EAAE,KAAK,EAAC,UAAU,EAAC,EAAE;gBACvB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACzB,UAAU,CAAC,KAAK,EAAE,CAAC;YACrB,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED,IAAI,CAAC,MAA2B,EAAE,MAAc,EAAE,MAAc,EAAE,QAAgB;QAChF,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;IACrH,CAAC;IAED,KAAK,CAAC,OAA0B,EAAE,QAAoC;QACpE,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACxG,CAAC;IAED,QAAQ,CAAC,OAAwC;QAC/C,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED,IAAI,CAAC,OAA4B;QAC/B,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,IAAI;QACF,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,QAAQ,CAAC,GAAY;QACnB,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,CAAC,KAAY,EAAE,KAAY;QAC/B,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IAED,KAAK,CACH,MAA2B,EAC3B,MAAe,EACf,MAAe,EACf,QAAiB;QAEjB,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC,CAC5E,IAAI,CAAC,EAAE,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,QAAQ,CACT,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA0B,EAAE,QAAoC;QACrE,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC/G,CAAC;IAED,SAAS,CAAC,IAAW,EAAE,OAAgC;QACrD,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC;CACF;AAlGD,gCAkGC"}