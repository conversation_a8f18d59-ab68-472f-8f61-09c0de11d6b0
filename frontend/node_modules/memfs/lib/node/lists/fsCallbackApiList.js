"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fsCallbackApiList = void 0;
exports.fsCallbackApiList = [
    'access',
    'appendFile',
    'chmod',
    'chown',
    'close',
    'copyFile',
    'createReadStream',
    'createWriteStream',
    'exists',
    'fchmod',
    'fchown',
    'fdatasync',
    'fstat',
    'fsync',
    'ftruncate',
    'futimes',
    'lchmod',
    'lchown',
    'link',
    'lstat',
    'mkdir',
    'mkdtemp',
    'open',
    'read',
    'readv',
    'readdir',
    'readFile',
    'readlink',
    'realpath',
    'rename',
    'rm',
    'rmdir',
    'stat',
    'symlink',
    'truncate',
    'unlink',
    'unwatchFile',
    'utimes',
    'lutimes',
    'watch',
    'watchFile',
    'write',
    'writev',
    'writeFile',
];
//# sourceMappingURL=fsCallbackApiList.js.map