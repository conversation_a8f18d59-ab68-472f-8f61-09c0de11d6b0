"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fsSynchronousApiList = void 0;
exports.fsSynchronousApiList = [
    'accessSync',
    'appendFileSync',
    'chmodSync',
    'chownSync',
    'closeSync',
    'copyFileSync',
    'existsSync',
    'fchmodSync',
    'fchownSync',
    'fdatasyncSync',
    'fstatSync',
    'fsyncSync',
    'ftruncateSync',
    'futimesSync',
    'lchmodSync',
    'lchownSync',
    'linkSync',
    'lstatSync',
    'mkdirSync',
    'mkdtempSync',
    'openSync',
    'readdirSync',
    'readFileSync',
    'readlinkSync',
    'readSync',
    'readvSync',
    'realpathSync',
    'renameSync',
    'rmdirSync',
    'rmSync',
    'statSync',
    'symlinkSync',
    'truncateSync',
    'unlinkSync',
    'utimesSync',
    'lutimesSync',
    'writeFileSync',
    'writeSync',
    'writevSync',
    // 'cpSync',
    // 'statfsSync',
];
//# sourceMappingURL=fsSynchronousApiList.js.map