"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FsaNodeSyncAdapterWorker = exports.FsaNodeFs = void 0;
var FsaNodeFs_1 = require("./FsaNodeFs");
Object.defineProperty(exports, "FsaNodeFs", { enumerable: true, get: function () { return FsaNodeFs_1.FsaNodeFs; } });
var FsaNodeSyncAdapterWorker_1 = require("./worker/FsaNodeSyncAdapterWorker");
Object.defineProperty(exports, "FsaNodeSyncAdapterWorker", { enumerable: true, get: function () { return FsaNodeSyncAdapterWorker_1.FsaNodeSyncAdapterWorker; } });
//# sourceMappingURL=index.js.map