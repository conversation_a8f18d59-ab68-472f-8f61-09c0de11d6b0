"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.decoder = exports.encoder = void 0;
const shared_1 = require("@jsonjoy.com/json-pack/lib/cbor/shared");
Object.defineProperty(exports, "encoder", { enumerable: true, get: function () { return shared_1.encoder; } });
Object.defineProperty(exports, "decoder", { enumerable: true, get: function () { return shared_1.decoder; } });
//# sourceMappingURL=json.js.map