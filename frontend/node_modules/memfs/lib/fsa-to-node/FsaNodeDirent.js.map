{"version": 3, "file": "FsaNodeDirent.js", "sourceRoot": "", "sources": ["../../src/fsa-to-node/FsaNodeDirent.ts"], "names": [], "mappings": ";;;AAEA,MAAa,aAAa;IACxB,YACkB,IAAc,EACX,IAA0B;QAD7B,SAAI,GAAJ,IAAI,CAAU;QACX,SAAI,GAAJ,IAAI,CAAsB;IAC5C,CAAC;IAEG,WAAW;QAChB,OAAO,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC;IACnC,CAAC;IAEM,MAAM;QACX,OAAO,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;IAC9B,CAAC;IAEM,aAAa;QAClB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,iBAAiB;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,cAAc;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,MAAM;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,QAAQ;QACb,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAjCD,sCAiCC"}