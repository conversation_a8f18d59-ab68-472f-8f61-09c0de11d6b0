{"version": 3, "file": "FsaNodeCore.js", "sourceRoot": "", "sources": ["../../src/fsa-to-node/FsaNodeCore.ts"], "names": [], "mappings": ";;;AAAA,uCAAiE;AACjE,iCAAwC;AACxC,iDAA2C;AAE3C,2DAAwD;AAExD,qCAAqC;AAKrC,MAAa,WAAW;IAItB,YACqB,IAA8E,EAC1F,WAAgC;QADpB,SAAI,GAAJ,IAAI,CAA0E;QAC1F,gBAAW,GAAX,WAAW,CAAqB;QAJtB,QAAG,GAAG,IAAI,GAAG,EAA6B,CAAC;QAqB9D;;;WAGG;QACH,gBAAW,GAAa,EAAE,CAAC;QAnBzB,IAAI,IAAI,YAAY,OAAO,EAAE,CAAC;YAC5B,IAAI;iBACD,IAAI,CAAC,IAAI,CAAC,EAAE;gBACV,IAAY,CAAC,IAAI,GAAG,IAAI,CAAC;YAC5B,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE,GAAE,CAAC,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;IAES,cAAc;QACtB,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;QACjC,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACjD,OAAO,OAAO,CAAC;IACjB,CAAC;IAQS,WAAW;QACnB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;QAC1C,OAAO,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC;IACxE,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,MAAM,CAAC,IAAc,EAAE,MAAe,EAAE,QAAiB;QACvE,IAAI,IAAI,GAAmC,MAAM,IAAI,CAAC,IAAI,CAAC;QAC3D,MAAM,OAAO,GAAkC,EAAE,MAAM,EAAE,CAAC;QAC1D,IAAI,CAAC;YACH,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;gBACxB,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,mBAAmB;wBACtB,MAAM,IAAA,kBAAW,EAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,wCAA8B,CAAC,CAAC;oBAClF,KAAK,eAAe;wBAClB,MAAM,IAAA,kBAAW,EAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,wCAA8B,CAAC,CAAC;gBACnF,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAES,KAAK,CAAC,OAAO,CACrB,IAAc,EACd,IAAY,EACZ,QAAiB,EACjB,MAAgB;QAEhB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACrD,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC;IACd,CAAC;IAES,KAAK,CAAC,YAAY,CAC1B,IAAc,EACd,IAAY,EACZ,QAAiB;QAEjB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,IAAI;YAAE,OAAO,GAAG,CAAC;QACtB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,mBAAmB;wBACtB,IAAI,CAAC;4BACH,OAAO,MAAM,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;wBAC5C,CAAC;wBAAC,OAAO,MAAM,EAAE,CAAC;4BAChB,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gCACzC,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;oCACpB,KAAK,mBAAmB;wCACtB,MAAM,IAAA,kBAAW,EAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,wCAA8B,CAAC,CAAC;oCAClF,KAAK,eAAe;wCAClB,MAAM,IAAA,kBAAW,EAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,wCAA8B,CAAC,CAAC;gCACnF,CAAC;4BACH,CAAC;wBACH,CAAC;oBACH,KAAK,eAAe;wBAClB,MAAM,IAAA,kBAAW,EAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,wCAA8B,CAAC,CAAC;gBACnF,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAES,WAAW,CAAC,EAAU,EAAE,QAAiB;QACjD,IAAI,CAAC,IAAA,WAAI,EAAC,EAAE,CAAC;YAAE,MAAM,SAAS,CAAC,kBAAM,CAAC,EAAE,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI;YAAE,MAAM,IAAA,kBAAW,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC;IACd,CAAC;IAES,KAAK,CAAC,gBAAgB,CAAC,EAAU,EAAE,QAAiB;QAC5D,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,EAAgB,EAAE,QAAiB;QAC5D,IAAI,OAAO,EAAE,KAAK,QAAQ;YAAE,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/E,MAAM,QAAQ,GAAG,IAAA,qBAAc,EAAC,EAAE,CAAC,CAAC;QACpC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,IAAA,qBAAc,EAAC,QAAQ,CAAC,CAAC;QAChD,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACpD,CAAC;IAES,KAAK,CAAC,mBAAmB,CAAC,EAAgB,EAAE,QAAiB;QACrE,IAAI,OAAO,EAAE,KAAK,QAAQ;YAAE,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/E,MAAM,QAAQ,GAAG,IAAA,qBAAc,EAAC,EAAE,CAAC,CAAC;QACpC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,IAAA,qBAAc,EAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACvD,OAAO,MAAM,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;IACzD,CAAC;IAES,KAAK,CAAC,MAAM,CAAC,QAAgB,EAAE,KAAa,EAAE,IAAY;QAClE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,IAAA,qBAAc,EAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,aAAa,GAAG,CAAC,CAAC,CAAC,KAAK,wBAAc,CAAC,CAAC;QAC9C,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;gBAChD,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,OAAO,GACX,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,CAAC,CAAC;gBACpG,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;wBACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;4BACnB,KAAK,mBAAmB;gCACtB,MAAM,IAAA,kBAAW,EAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;4BACjD,KAAK,eAAe;gCAClB,MAAM,IAAA,kBAAW,EAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;wBAClD,CAAC;oBACH,CAAC;oBACD,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,wBAAe,CAAC,CAAC;YACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;YAC1E,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,mBAAmB;wBACtB,MAAM,IAAA,kBAAW,EAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;oBACjD,KAAK,eAAe;wBAClB,MAAM,IAAA,kBAAW,EAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAES,OAAO,CACf,OAAkC,EAClC,QAAgB,EAChB,KAAa,EACb,IAAY;QAEZ,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC9B,MAAM,IAAI,GAAG,IAAI,qCAAiB,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACvE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAES,KAAK,CAAC,OAAO,CAAC,EAAU;QAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAC1D,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpC,IAAI,OAAO;YAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACzC,CAAC;IAES,WAAW,CAAC,EAAgB;QACpC,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAClC,IAAI,CAAC,QAAQ;gBAAE,MAAM,IAAA,kBAAW,EAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YACtD,OAAO,QAAQ,CAAC,QAAQ,CAAC;QAC3B,CAAC;QACD,OAAO,IAAA,qBAAc,EAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;;AApMH,kCAqMC;AApMkB,cAAE,GAAW,UAAU,AAArB,CAAsB"}