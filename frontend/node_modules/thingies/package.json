{"name": "thingies", "version": "1.21.0", "description": "", "author": {"name": "<PERSON>ich", "url": "https://github.com/streamich"}, "homepage": "https://github.com/streamich/thingies", "repository": "streamich/thingies", "license": "Unlicense", "engines": {"node": ">=10.18"}, "main": "lib/index.js", "module": "es6/index.js", "files": ["lib/", "es6/", "es2020/"], "types": "lib/index.d.ts", "typings": "lib/index.d.ts", "scripts": {"prettier": "prettier --ignore-path .gitignore --write 'src/**/*.{ts,tsx,js,jsx}'", "prettier:diff": "prettier -l 'src/**/*.{ts,tsx,js,jsx}'", "tslint": "tslint 'src/**/*.{js,jsx,ts,tsx}' -t verbose", "lint": "yarn tslint", "clean": "rimraf lib es6 es2020", "build:cjs": "tsc", "build:es6": "tsc --module commonjs --target es6 --outDir es6", "build:es2020": "tsc --project tsconfig.build.json --module commonjs --target es2020 --outDir es2020", "build": "yarn build:cjs && yarn build:es6 && yarn build:es2020", "test": "jest --no-cache --config='jest.config.js'", "release": "semantic-release"}, "keywords": [], "dependencies": {}, "peerDependencies": {"tslib": "^2"}, "devDependencies": {"@semantic-release/changelog": "^6.0.0", "@semantic-release/git": "^10.0.0", "@semantic-release/npm": "^8.0.2", "@types/jest": "^29.5.12", "husky": "^8.0.0", "jest": "^29.7.0", "prettier": "^3.0.0", "pretty-quick": "^3.1.1", "rimraf": "^3.0.2", "semantic-release": "^18.0.0", "ts-jest": "^29.1.2", "tslint": "^6.1.3", "tslint-config-common": "^1.6.2", "typescript": "^5.0.3", "tslib": "^2.6.2"}, "release": {"verifyConditions": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "prepare": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "branches": ["main"]}}