import { <PERSON><PERSON><PERSON><PERSON>, I<PERSON>riterGrowable } from '@jsonjoy.com/util/lib/buffers';
import { JsonPackExtension } from '../JsonPackExtension';
import { BinaryJsonEncoder, TlvBinaryJsonEncoder } from '../types';
import { IMessagePackEncoder } from './types';
export declare class MsgPackEncoderFast<W extends IWriter & IWriterGrowable = IWriter & IWriterGrowable> implements IMessagePackEncoder, BinaryJsonEncoder, TlvBinaryJsonEncoder {
    readonly writer: W;
    constructor(writer?: W);
    encode(json: unknown): Uint8Array;
    encodeAny(json: unknown): void;
    writeAny(value: unknown): void;
    protected encodeFloat64(num: number): void;
    writeNull(): void;
    writeFloat(float: number): void;
    u32(num: number): void;
    n32(num: number): void;
    encodeNumber(num: number): void;
    writeNumber(num: number): void;
    writeInteger(int: number): void;
    writeUInteger(uint: number): void;
    encodeNull(): void;
    encodeTrue(): void;
    encodeFalse(): void;
    encodeBoolean(bool: boolean): void;
    writeBoolean(bool: boolean): void;
    encodeStringHeader(length: number): void;
    writeStrHdr(length: number): void;
    encodeString(str: string): void;
    writeStr(str: string): void;
    encodeAsciiString(str: string): void;
    writeAsciiStr(str: string): void;
    encodeArrayHeader(length: number): void;
    encodeArray(arr: unknown[]): void;
    writeArrHdr(length: number): void;
    writeArr(arr: unknown[]): void;
    encodeObjectHeader(length: number): void;
    encodeObject(obj: Record<string, unknown>): void;
    writeObjHdr(length: number): void;
    writeObj(obj: Record<string, unknown>): void;
    encodeExtHeader(type: number, length: number): void;
    encodeExt(ext: JsonPackExtension): void;
    encodeBinaryHeader(length: number): void;
    encodeBinary(buf: Uint8Array): void;
    writeBinHdr(length: number): void;
    writeBin(buf: Uint8Array): void;
}
