{"version": 3, "file": "CborDecoderBase.js", "sourceRoot": "", "sources": ["../../src/cbor/CborDecoderBase.ts"], "names": [], "mappings": ";;;;AACA,2DAA4D;AAC5D,4DAAuD;AACvD,oDAA+C;AAC/C,iEAA4D;AAC5D,iIAAiG;AAKjG,MAAa,eAAe;IAG1B,YACS,SAAY,IAAI,eAAM,EAAS,EACtB,aAAgC,iCAAuB;QADhE,WAAM,GAAN,MAAM,CAAyB;QACtB,eAAU,GAAV,UAAU,CAA6C;IACtE,CAAC;IAEG,IAAI,CAAC,KAAiB;QAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,GAAG,EAAe,CAAC;IACjC,CAAC;IAEM,MAAM,CAAC,KAAiB;QAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;IACpB,CAAC;IAIM,GAAG;QACR,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,IAAI,KAAK,IAAY,EAAE,CAAC;YACtB,IAAI,KAAK,IAAY;gBAAE,OAAO,KAAK,MAAc,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;;gBAC3F,OAAO,KAAK,MAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9E,CAAC;aAAM,CAAC;YACN,IAAI,KAAK,IAAY;gBAAE,OAAO,KAAK,MAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;gBACzF,OAAO,KAAK,MAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAEM,UAAU,CAAC,KAAa;QAC7B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,IAAI,KAAK,IAAY,EAAE,CAAC;YACtB,IAAI,KAAK,IAAY;gBAAE,OAAO,KAAK,MAAc,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;;gBAC3F,OAAO,KAAK,MAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9E,CAAC;aAAM,CAAC;YACN,IAAI,KAAK,IAAY;gBAAE,OAAO,KAAK,MAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;gBACzF,OAAO,KAAK,MAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAEM,YAAY,CAAC,KAAa;QAC/B,IAAI,KAAK,GAAG,EAAE;YAAE,OAAO,KAAK,CAAC;QAC7B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;YAC1B,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC3B,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC3B,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YACnC,KAAK,EAAE;gBACL,OAAO,CAAC,CAAC,CAAC;YACZ;gBACE,QAA6B;QACjC,CAAC;IACH,CAAC;IAIM,QAAQ,CAAC,KAAa;QAC3B,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YACf,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;gBACf,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;gBAC9B,OAAO,GAAG,mBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;IACH,CAAC;IAIM,QAAQ,CAAC,KAAa;QAC3B,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YACf,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;gBACf,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACxE,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;gBAC9B,OAAO,GAAG,GAAG,mBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;IACH,CAAC;IAIM,OAAO,CAAC,KAAa;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC1C,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YACjC,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YAClC,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YAClC,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC1C,KAAK,EAAE,CAAC,CAAC,CAAC;gBACR,IAAI,IAAI,GAAG,CAAC,CAAC;gBACb,MAAM,IAAI,GAAiB,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc,EAAE,CAAC;oBACxC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBAClC,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC;oBACrB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,CAAC;gBACD,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;gBAChB,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;gBACjC,IAAI,MAAM,GAAG,CAAC,CAAC;gBACf,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAChC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;oBACpB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;oBACrB,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC;gBACvB,CAAC;gBACD,OAAO,GAAG,CAAC;YACb,CAAC;YACD;gBACE,QAA6B;QACjC,CAAC;IACH,CAAC;IAEM,YAAY;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,IAAI,KAAK,MAAc;YAAE,QAAuC;QAChE,IAAI,KAAK,GAAG,EAAE;YAAE,QAAuC;QACvD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAIM,SAAS;QACd,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,IAAI,KAAK,MAAc;YAAE,SAAiC;QAC1D,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAEM,OAAO,CAAC,KAAa;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAClC,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YACnC,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YACnC,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC3C,KAAK,EAAE,CAAC,CAAC,CAAC;gBACR,IAAI,GAAG,GAAG,EAAE,CAAC;gBACb,OAAO,MAAM,CAAC,IAAI,EAAE,QAAc;oBAAE,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC/D,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;gBAChB,OAAO,GAAG,CAAC;YACb,CAAC;YACD;gBACE,QAA6B;QACjC,CAAC;IACH,CAAC;IAEM,UAAU,CAAC,KAAa;QAC7B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,KAAK,CAAC;QAC9B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;YAC1B,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC3B,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC3B,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YACnC;gBACE,QAA6B;QACjC,CAAC;IACH,CAAC;IAEM,YAAY;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,IAAI,KAAK,MAAc;YAAE,QAAuC;QAChE,IAAI,KAAK,GAAG,EAAE;YAAE,QAAuC;QACvD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAIM,OAAO,CAAC,KAAa;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;IAC7B,CAAC;IAEM,UAAU,CAAC,MAAc;QAC9B,MAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;YAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACtD,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,YAAY;QACjB,MAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc;YAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAChB,OAAO,GAAG,CAAC;IACb,CAAC;IAIM,OAAO,CAAC,KAAa;QAC1B,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YACf,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,QAAQ,KAAK,EAAE,CAAC;gBACd,KAAK,EAAE;oBACL,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;oBAC1B,MAAM;gBACR,KAAK,EAAE;oBACL,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;oBAC3B,MAAM;gBACR,KAAK,EAAE;oBACL,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;oBAC3B,MAAM;gBACR,KAAK,EAAE;oBACL,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;oBACnC,MAAM;YACV,CAAC;YACD,MAAM,GAAG,GAA4B,EAAE,CAAC;YACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAChC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACvB,IAAI,GAAG,KAAK,WAAW;oBAAE,QAA+B;gBACxD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACzB,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACnB,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC;aAAM,IAAI,KAAK,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;;YAC/C,QAA6B;IACpC,CAAC;IAGM,UAAU,CAAC,MAAc;QAC9B,MAAM,GAAG,GAA4B,EAAE,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACzB,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,YAAY;QACjB,MAAM,GAAG,GAA4B,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc,EAAE,CAAC;YACxC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc;gBAAE,QAAiC;YACvE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACzB,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAChB,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,IAAI,KAAK,MAAc;YAAE,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,MAAM,GAAG,EAAE;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAC7E,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,OAAO,GAAG,CAAC;IACb,CAAC;IAIM,OAAO,CAAC,KAAa;QAC1B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC/C,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3C,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5C,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5C,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACpD;gBACE,QAA6B;QACjC,CAAC;IACH,CAAC;IAEM,UAAU,CAAC,GAAW;QAC3B,OAAO,IAAI,qCAAiB,CAAU,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IACzD,CAAC;IAIM,OAAO,CAAC,KAAa;QAC1B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,IAAI,KAAmB;gBAC1B,OAAO,KAAK,CAAC;YACf,KAAK,IAAI,KAAmB;gBAC1B,OAAO,IAAI,CAAC;YACd,KAAK,IAAI,KAAmB;gBAC1B,OAAO,IAAI,CAAC;YACd,KAAK,IAAI,KAAmB;gBAC1B,OAAO,SAAS,CAAC;YACnB,KAAK,IAAI,KAAmB;gBAC1B,OAAO,IAAI,6BAAa,CAAS,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YACrD,KAAK,IAAI,KAAmB;gBAC1B,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;YACpB,KAAK,IAAI,KAAmB;gBAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC3B,KAAK,IAAI,KAAmB;gBAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QAC7B,CAAC;QACD,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,IAAI,6BAAa,CAAS,KAAK,CAAC,CAAC;QACzD,QAA6B;IAC/B,CAAC;IAEM,GAAG;QACR,OAAO,IAAA,eAAS,EAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;IACtC,CAAC;CACF;AAjVD,0CAiVC"}