import type { IRead<PERSON>, IReaderResettable } from '@jsonjoy.com/util/lib/buffers';
import type { BinaryJsonDecoder, PackValue } from '../types';
export declare class RespDecoder<R extends IReader & IReaderResettable = IReader & IReaderResettable> implements BinaryJsonDecoder {
    reader: R;
    tryUtf8: boolean;
    constructor(reader?: R);
    read(uint8: Uint8Array): PackValue;
    decode(uint8: Uint8Array): unknown;
    val(): unknown;
    protected readLength(): number;
    readCmd(): [cmd: string, ...args: Uint8Array[]];
    readBool(): boolean;
    readInt(): number;
    readFloat(): number;
    readBigint(): bigint;
    readStrSimple(): string;
    readStrBulk(): Uint8Array | string | null;
    readAsciiAsStrBulk(): string;
    readStrVerbatim(): string | Uint8Array;
    readErrSimple(): Error;
    readErrBulk(): Error;
    readArr(): unknown[] | null;
    readSet(): Set<unknown>;
    readObj(): Record<string, unknown>;
    skipN(n: number): void;
    skipAny(): void;
    skipBool(): void;
    skipInt(): void;
    skipFloat(): void;
    skipBigint(): void;
    skipStrSimple(): void;
    skipStrBulk(): void;
    skipStrVerbatim(): void;
    skipErrSimple(): void;
    skipErrBulk(): void;
    skipArr(): void;
    skipSet(): void;
    skipObj(): void;
}
