/*! For license information please see main.bedb7068bf18577fa311.js.LICENSE.txt */
(()=>{"use strict";var e,t,n,r={1498:(e,t,n)=>{var r=n(6540),a=n(5338),o=n(867),i=n(5448),l=n(1430),c=n(4716),s=n(7441),u=n(4976),m=n(7767),p=n(3853),A=n(2941),f=n(7206),d=n(2120),g=n(761),y=n(1427),b=n(7977),E=n(8990),h=n(7450),x=n(2877),v=n(980),C=n(8602),k=n(9248),w=n(4336),B=n(6157),S=n(1201),_=n(562),P=n(462);function I(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var O=i.A.Sider;const j=function(e){var t,n,a=e.collapsed,o=e.setCollapsed,i=(0,m.zy)(),l=(0,m.Zp)(),c=(t=(0,r.useState)(3),n=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(t,n)||function(e,t){if(e){if("string"==typeof e)return I(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?I(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),s=c[0],u=(c[1],[{key:"/",icon:r.createElement(E.A,null),label:"Dashboard"},{key:"/profiles",icon:r.createElement(h.A,null),label:"Profile Manager"},{key:"/scraping",icon:r.createElement(x.A,null),label:"Facebook Scraping"},{key:"/messaging",icon:r.createElement(v.A,null),label:"Bulk Messaging"},{key:"/settings",icon:r.createElement(C.A,null),label:"Settings"}]),p=[{key:"profile",icon:r.createElement(h.A,null),label:"Profile Settings"},{key:"about",icon:r.createElement(k.A,null),label:"About Application"},{type:"divider"},{key:"logout",icon:r.createElement(w.A,null),label:"Exit Application",onClick:function(){window.electronAPI&&window.electronAPI.closeApp()}}];return r.createElement(O,{trigger:null,collapsible:!0,collapsed:a,width:280,style:{overflow:"auto",height:"100vh",position:"fixed",left:0,top:0,bottom:0,background:"linear-gradient(180deg, #667eea 0%, #764ba2 100%)",boxShadow:"4px 0 20px rgba(0,0,0,0.1)",zIndex:1e3}},r.createElement("div",{className:"sidebar-logo",style:{height:"80px",display:"flex",alignItems:"center",justifyContent:"center",background:"rgba(255, 255, 255, 0.1)",margin:"16px",borderRadius:"16px",backdropFilter:"blur(10px)",border:"1px solid rgba(255, 255, 255, 0.2)"}},r.createElement("div",{className:"logo-container",style:{display:"flex",alignItems:"center",gap:a?"0":"12px"}},a?r.createElement("div",{className:"logo-icon-collapsed",style:{fontSize:"32px",filter:"drop-shadow(0 2px 4px rgba(0,0,0,0.3))"}},"📱"):r.createElement(r.Fragment,null,r.createElement("div",{className:"logo-icon",style:{fontSize:"36px",filter:"drop-shadow(0 2px 4px rgba(0,0,0,0.3))"}},"📱"),r.createElement("div",{className:"logo-text",style:{color:"white",lineHeight:1.2}},r.createElement("div",{style:{fontSize:"18px",fontWeight:"bold",margin:0}},"Facebook"),r.createElement("div",{style:{fontSize:"14px",opacity:.9,margin:0}},"Automation"))))),r.createElement("div",{style:{padding:"0 16px",marginBottom:"16px",display:"flex",justifyContent:a?"center":"flex-end"}},r.createElement(A.Ay,{type:"text",icon:a?r.createElement(B.A,null):r.createElement(S.A,null),onClick:function(){return o(!a)},style:{fontSize:"16px",width:"40px",height:"40px",color:"white",background:"rgba(255, 255, 255, 0.1)",border:"1px solid rgba(255, 255, 255, 0.2)",borderRadius:"12px",display:"flex",alignItems:"center",justifyContent:"center"}})),r.createElement(f.A,{theme:"dark",mode:"inline",selectedKeys:[i.pathname],items:u,onClick:function(e){var t=e.key;return l(t)},style:{background:"transparent",border:"none",padding:"0 16px"},className:"modern-sidebar-menu"}),r.createElement("div",{style:{position:"absolute",bottom:"20px",left:"16px",right:"16px",background:"rgba(255, 255, 255, 0.1)",borderRadius:"16px",padding:"16px",backdropFilter:"blur(10px)",border:"1px solid rgba(255, 255, 255, 0.2)"}},a?r.createElement("div",{style:{textAlign:"center"}},r.createElement(b.A,{title:"Notifications",placement:"right"},r.createElement(d.A,{count:s,size:"small"},r.createElement(_.A,{style:{color:"white",fontSize:"20px",marginBottom:"12px"}}))),r.createElement(g.A,{menu:{items:p},placement:"topRight",arrow:!0,trigger:["click"]},r.createElement(y.A,{style:{backgroundColor:"rgba(255, 255, 255, 0.2)",border:"2px solid rgba(255, 255, 255, 0.3)",cursor:"pointer"},icon:r.createElement(h.A,null),size:"large"}))):r.createElement("div",null,r.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"}},r.createElement("span",{style:{color:"rgba(255,255,255,0.8)",fontSize:"12px"}},"NOTIFICATIONS"),r.createElement(d.A,{count:s,size:"small"},r.createElement(_.A,{style:{color:"white",fontSize:"16px"}}))),r.createElement(g.A,{menu:{items:p},placement:"topRight",arrow:!0,trigger:["click"]},r.createElement("div",{style:{display:"flex",alignItems:"center",cursor:"pointer",padding:"8px",borderRadius:"12px",transition:"background 0.3s",background:"rgba(255, 255, 255, 0.1)"}},r.createElement(y.A,{style:{backgroundColor:"rgba(255, 255, 255, 0.2)",border:"2px solid rgba(255, 255, 255, 0.3)",marginRight:"12px"},icon:r.createElement(h.A,null)}),r.createElement("div",{style:{flex:1}},r.createElement("div",{style:{color:"white",fontWeight:"bold",fontSize:"14px"}},"Administrator"),r.createElement("div",{style:{color:"rgba(255,255,255,0.7)",fontSize:"12px"}},"System Admin")),r.createElement(P.A,{style:{color:"rgba(255,255,255,0.6)",fontSize:"16px"}}))))),r.createElement("style",{jsx:!0},"\n        .modern-sidebar-menu .ant-menu-item {\n          margin: 6px 0 !important;\n          border-radius: 12px !important;\n          height: 48px !important;\n          line-height: 48px !important;\n          transition: all 0.3s ease !important;\n          border: 1px solid transparent !important;\n        }\n\n        .modern-sidebar-menu .ant-menu-item:hover {\n          background: rgba(255, 255, 255, 0.15) !important;\n          transform: translateX(4px);\n          border: 1px solid rgba(255, 255, 255, 0.2) !important;\n        }\n\n        .modern-sidebar-menu .ant-menu-item-selected {\n          background: rgba(255, 255, 255, 0.25) !important;\n          border: 1px solid rgba(255, 255, 255, 0.3) !important;\n          box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;\n        }\n\n        .modern-sidebar-menu .ant-menu-item-selected::after {\n          display: none !important;\n        }\n\n        .modern-sidebar-menu .ant-menu-item .ant-menu-item-icon {\n          font-size: 18px;\n          margin-right: 12px;\n        }\n\n        .modern-sidebar-menu .ant-menu-item-selected .ant-menu-item-icon {\n          color: white !important;\n        }\n\n        .modern-sidebar-menu .ant-menu-item-selected span {\n          color: white !important;\n          font-weight: bold;\n        }\n\n        .modern-sidebar-menu .ant-menu-item span {\n          font-size: 14px;\n          font-weight: 500;\n        }\n\n        /* Scrollbar Styling */\n        .ant-layout-sider::-webkit-scrollbar {\n          width: 6px;\n        }\n\n        .ant-layout-sider::-webkit-scrollbar-track {\n          background: rgba(255, 255, 255, 0.1);\n          border-radius: 3px;\n        }\n\n        .ant-layout-sider::-webkit-scrollbar-thumb {\n          background: rgba(255, 255, 255, 0.3);\n          border-radius: 3px;\n        }\n\n        .ant-layout-sider::-webkit-scrollbar-thumb:hover {\n          background: rgba(255, 255, 255, 0.5);\n        }\n      "))};var z=n(9957),T=n(8226),F=n(6449),D=n(2454),R=n(7852);function M(e){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},M(e)}function U(e,t,n){return(t=function(e){var t=function(e){if("object"!=M(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==M(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function N(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return L(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?L(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var q=i.A.Header,W=z.A.Search;const G=function(e){var t=e.collapsed,n=e.setCollapsed,a=N((0,r.useState)([{id:1,title:"System Update",message:"New version available",time:"2 min ago",type:"info"},{id:2,title:"Task Completed",message:"Scraping task finished successfully",time:"5 min ago",type:"success"},{id:3,title:"Warning",message:"High memory usage detected",time:"10 min ago",type:"warning"}]),2),o=a[0],i=(a[1],N((0,r.useState)("connected"),2)),c=i[0],s=(i[1],N((0,r.useState)(new Date),2)),u=s[0],m=s[1];(0,r.useEffect)(function(){var e=setInterval(function(){m(new Date)},1e3);return function(){return clearInterval(e)}},[]);var p=[{key:"profile",icon:r.createElement(h.A,null),label:"Profile Settings"},{key:"about",icon:r.createElement(k.A,null),label:"About Application"},{type:"divider"},{key:"logout",icon:r.createElement(w.A,null),label:"Exit Application",onClick:function(){window.electronAPI&&window.electronAPI.closeApp()}}],f=o.map(function(e){return{key:e.id,label:r.createElement("div",{style:{width:"300px",padding:"8px 0"}},r.createElement("div",{style:{fontWeight:"bold",marginBottom:"4px"}},e.title),r.createElement("div",{style:{fontSize:"12px",color:"#666",marginBottom:"4px"}},e.message),r.createElement("div",{style:{fontSize:"11px",color:"#999"}},e.time))}}),E=function(e){if(window.electronAPI)switch(e){case"minimize":window.electronAPI.minimizeWindow();break;case"maximize":window.electronAPI.maximizeWindow();break;case"close":window.electronAPI.closeWindow()}};return r.createElement(q,{className:"modern-header",style:U(U(U(U({position:"fixed",top:0,zIndex:999,width:"100%",display:"flex",alignItems:"center",justifyContent:"space-between",background:"rgba(255, 255, 255, 0.95)",backdropFilter:"blur(20px)",boxShadow:"0 2px 20px rgba(0,0,0,0.1)",padding:"0 24px",marginLeft:t?80:280},"width",t?"calc(100% - 80px)":"calc(100% - 280px)"),"transition","all 0.3s ease"),"border","none"),"borderBottom","1px solid rgba(0,0,0,0.06)")},r.createElement("div",{style:{display:"flex",alignItems:"center",gap:"16px"}},r.createElement(A.Ay,{type:"text",icon:t?r.createElement(B.A,null):r.createElement(S.A,null),onClick:function(){return n(!t)},style:{fontSize:"18px",width:"40px",height:"40px",borderRadius:"12px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",border:"none",display:"flex",alignItems:"center",justifyContent:"center",boxShadow:"0 4px 12px rgba(102, 126, 234, 0.3)"}}),r.createElement("div",null,r.createElement("h1",{style:{margin:0,fontSize:"24px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",fontWeight:"bold"}},"Facebook Automation Desktop"),r.createElement("div",{style:{fontSize:"12px",color:"#666",marginTop:"-2px"}},u.toLocaleString()))),r.createElement("div",{style:{flex:1,maxWidth:"400px",margin:"0 24px"}},r.createElement(W,{placeholder:"Search profiles, tasks, or settings...",allowClear:!0,enterButton:r.createElement(x.A,null),size:"large",onSearch:function(e){e&&l.Ay.info({message:"Search",description:"Searching for: ".concat(e),duration:2})},style:{borderRadius:"12px"}})),r.createElement("div",{style:{display:"flex",alignItems:"center",gap:"12px"}},r.createElement(b.A,{title:"Connection: ".concat(c)},r.createElement("div",{style:{display:"flex",alignItems:"center",gap:"6px",padding:"6px 12px",background:"rgba(0,0,0,0.04)",borderRadius:"20px",fontSize:"12px"}},r.createElement(T.A,{style:{color:function(){switch(c){case"connected":return"#52c41a";case"connecting":return"#1890ff";case"disconnected":return"#ff4d4f";default:return"#d9d9d9"}}()}}),r.createElement("span",{style:{color:"#666",textTransform:"capitalize"}},c))),r.createElement(b.A,{title:"System Performance"},r.createElement("div",{style:{display:"flex",alignItems:"center",gap:"6px",padding:"6px 12px",background:"rgba(0,0,0,0.04)",borderRadius:"20px",fontSize:"12px"}},r.createElement(P.A,{style:{color:"#52c41a"}}),r.createElement("span",{style:{color:"#666"}},"Optimal"))),r.createElement(g.A,{menu:{items:f},placement:"bottomRight",arrow:!0,trigger:["click"]},r.createElement(A.Ay,{type:"text",style:{width:"40px",height:"40px",borderRadius:"12px",display:"flex",alignItems:"center",justifyContent:"center"}},r.createElement(d.A,{count:o.length,size:"small"},r.createElement(_.A,{style:{fontSize:"18px",color:"#666"}})))),r.createElement(b.A,{title:"Settings"},r.createElement(A.Ay,{type:"text",icon:r.createElement(C.A,null),onClick:function(){return window.location.hash="/settings"},style:{fontSize:"18px",width:"40px",height:"40px",borderRadius:"12px",color:"#666",display:"flex",alignItems:"center",justifyContent:"center"}})),r.createElement(g.A,{menu:{items:p},placement:"bottomRight",arrow:!0,trigger:["click"]},r.createElement("div",{style:{display:"flex",alignItems:"center",cursor:"pointer",padding:"4px 12px",borderRadius:"12px",background:"rgba(0,0,0,0.04)",transition:"background 0.3s"}},r.createElement(y.A,{style:{backgroundColor:"#667eea",marginRight:"8px"},icon:r.createElement(h.A,null)}),r.createElement("div",{style:{display:"flex",flexDirection:"column"}},r.createElement("span",{style:{fontSize:"14px",fontWeight:"bold",color:"#333"}},"Admin"),r.createElement("span",{style:{fontSize:"12px",color:"#666"}},"Administrator")))),r.createElement("div",{style:{display:"flex",gap:"4px",marginLeft:"12px"}},r.createElement(A.Ay,{type:"text",icon:r.createElement(F.A,null),onClick:function(){return E("minimize")},style:{width:"32px",height:"32px",borderRadius:"8px",fontSize:"12px",color:"#666",display:"flex",alignItems:"center",justifyContent:"center"}}),r.createElement(A.Ay,{type:"text",icon:r.createElement(D.A,null),onClick:function(){return E("maximize")},style:{width:"32px",height:"32px",borderRadius:"8px",fontSize:"12px",color:"#666",display:"flex",alignItems:"center",justifyContent:"center"}}),r.createElement(A.Ay,{type:"text",icon:r.createElement(R.A,null),onClick:function(){return E("close")},style:{width:"32px",height:"32px",borderRadius:"8px",fontSize:"12px",color:"#ff4d4f",display:"flex",alignItems:"center",justifyContent:"center"}}))),r.createElement("style",{jsx:!0},"\n        .modern-header .ant-input-search .ant-input-group .ant-input-affix-wrapper {\n          border-radius: 12px 0 0 12px;\n          border-right: none;\n        }\n\n        .modern-header .ant-input-search .ant-input-group .ant-btn {\n          border-radius: 0 12px 12px 0;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          border-color: #667eea;\n        }\n\n        .modern-header .ant-input-search .ant-input-group .ant-btn:hover {\n          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n          border-color: #5a6fd8;\n        }\n\n        .modern-header .ant-btn:hover {\n          background: rgba(0,0,0,0.06) !important;\n        }\n      "))};var Y=n(9381),H=n(7152),V=n(6370),K=n(2702),Z=n(7260),X=n(6395),Q=n(7122),$=n(7983),J=n(3835),ee=n(2652),te=n(6914),ne=n(7308),re=n(2734),ae=n(581),oe=n(1295),ie=n(6490),le=n(7942),ce=n(2786),se=n(2977),ue=n(88),me=n(4890),pe=n(5132),Ae=n(1083);function fe(e){return fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fe(e)}function de(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function o(n,r,a,o){var c=r&&r.prototype instanceof l?r:l,s=Object.create(c.prototype);return ge(s,"_invoke",function(n,r,a){var o,l,c,s=0,u=a||[],m=!1,p={p:0,n:0,v:e,a:A,f:A.bind(e,4),d:function(t,n){return o=t,l=0,c=e,p.n=n,i}};function A(n,r){for(l=n,c=r,t=0;!m&&s&&!a&&t<u.length;t++){var a,o=u[t],A=p.p,f=o[2];n>3?(a=f===r)&&(c=o[(l=o[4])?5:(l=3,3)],o[4]=o[5]=e):o[0]<=A&&((a=n<2&&A<o[1])?(l=0,p.v=r,p.n=o[1]):A<f&&(a=n<3||o[0]>r||r>f)&&(o[4]=n,o[5]=r,p.n=f,l=0))}if(a||n>1)return i;throw m=!0,r}return function(a,u,f){if(s>1)throw TypeError("Generator is already running");for(m&&1===u&&A(u,f),l=u,c=f;(t=l<2?e:c)||!m;){o||(l?l<3?(l>1&&(p.n=-1),A(l,c)):p.n=c:p.v=c);try{if(s=2,o){if(l||(a="next"),t=o[a]){if(!(t=t.call(o,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,l<2&&(l=0)}else 1===l&&(t=o.return)&&t.call(o),l<2&&(c=TypeError("The iterator does not provide a '"+a+"' method"),l=1);o=e}else if((t=(m=p.n<0)?c:n.call(r,p))!==i)break}catch(t){o=e,l=1,c=t}finally{s=1}}return{value:t,done:m}}}(n,a,o),!0),s}var i={};function l(){}function c(){}function s(){}t=Object.getPrototypeOf;var u=[][r]?t(t([][r]())):(ge(t={},r,function(){return this}),t),m=s.prototype=l.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,ge(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e}return c.prototype=s,ge(m,"constructor",s),ge(s,"constructor",c),c.displayName="GeneratorFunction",ge(s,a,"GeneratorFunction"),ge(m),ge(m,a,"Generator"),ge(m,r,function(){return this}),ge(m,"toString",function(){return"[object Generator]"}),(de=function(){return{w:o,m:p}})()}function ge(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}ge=function(e,t,n,r){if(t)a?a(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var o=function(t,n){ge(e,t,function(e){return this._invoke(t,n,e)})};o("next",0),o("throw",1),o("return",2)}},ge(e,t,n,r)}function ye(e,t,n,r,a,o,i){try{var l=e[o](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,a)}function be(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var o=e.apply(t,n);function i(e){ye(o,r,a,i,l,"next",e)}function l(e){ye(o,r,a,i,l,"throw",e)}i(void 0)})}}function Ee(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,he(r.key),r)}}function he(e){var t=function(e){if("object"!=fe(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=fe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==fe(t)?t:t+""}var xe=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.baseURL=null,this.client=null,this.init()},t=[{key:"init",value:(k=be(de().m(function e(){var t;return de().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,window.electronAPI.getBackendUrl();case 1:this.baseURL=e.v,this.client=Ae.A.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.client.interceptors.request.use(function(e){var t;return console.log("API Request: ".concat(null===(t=e.method)||void 0===t?void 0:t.toUpperCase()," ").concat(e.url)),e},function(e){return console.error("API Request Error:",e),Promise.reject(e)}),this.client.interceptors.response.use(function(e){return console.log("API Response: ".concat(e.status," ").concat(e.config.url)),e.data},function(e){if(console.error("API Response Error:",e),e.response){var t=e.response,n=t.status,r=t.data;throw new Error(r.detail||r.message||"HTTP ".concat(n," Error"))}throw e.request?new Error("No response from server. Please check if the backend is running."):new Error(e.message||"Unknown error occurred")}),e.n=3;break;case 2:throw e.p=2,t=e.v,console.error("Failed to initialize API service:",t),t;case 3:return e.a(2)}},e,this,[[0,2]])})),function(){return k.apply(this,arguments)})},{key:"get",value:(C=be(de().m(function e(t){var n,r=arguments;return de().w(function(e){for(;;)if(0===e.n)return n=r.length>1&&void 0!==r[1]?r[1]:{},e.a(2,this.client.get(t,n))},e,this)})),function(e){return C.apply(this,arguments)})},{key:"post",value:(v=be(de().m(function e(t){var n,r,a=arguments;return de().w(function(e){for(;;)if(0===e.n)return n=a.length>1&&void 0!==a[1]?a[1]:{},r=a.length>2&&void 0!==a[2]?a[2]:{},e.a(2,this.client.post(t,n,r))},e,this)})),function(e){return v.apply(this,arguments)})},{key:"put",value:(x=be(de().m(function e(t){var n,r,a=arguments;return de().w(function(e){for(;;)if(0===e.n)return n=a.length>1&&void 0!==a[1]?a[1]:{},r=a.length>2&&void 0!==a[2]?a[2]:{},e.a(2,this.client.put(t,n,r))},e,this)})),function(e){return x.apply(this,arguments)})},{key:"delete",value:(h=be(de().m(function e(t){var n,r=arguments;return de().w(function(e){for(;;)if(0===e.n)return n=r.length>1&&void 0!==r[1]?r[1]:{},e.a(2,this.client.delete(t,n))},e,this)})),function(e){return h.apply(this,arguments)})},{key:"getProfiles",value:(E=be(de().m(function e(){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.get("/api/profiles/"))},e,this)})),function(){return E.apply(this,arguments)})},{key:"createProfile",value:(b=be(de().m(function e(t){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.post("/api/profiles/",t))},e,this)})),function(e){return b.apply(this,arguments)})},{key:"updateProfile",value:(y=be(de().m(function e(t,n){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.put("/api/profiles/".concat(t),n))},e,this)})),function(e,t){return y.apply(this,arguments)})},{key:"deleteProfile",value:(g=be(de().m(function e(t){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.delete("/api/profiles/".concat(t)))},e,this)})),function(e){return g.apply(this,arguments)})},{key:"testProfile",value:(d=be(de().m(function e(t){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.post("/api/profiles/".concat(t,"/test")))},e,this)})),function(e){return d.apply(this,arguments)})},{key:"loginFacebook",value:(f=be(de().m(function e(t,n){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.post("/api/profiles/".concat(t,"/login"),n))},e,this)})),function(e,t){return f.apply(this,arguments)})},{key:"startScraping",value:(A=be(de().m(function e(t){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.post("/api/scraping/start",t))},e,this)})),function(e){return A.apply(this,arguments)})},{key:"getScrapingStatus",value:(p=be(de().m(function e(t){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.get("/api/scraping/status/".concat(t)))},e,this)})),function(e){return p.apply(this,arguments)})},{key:"stopScraping",value:(m=be(de().m(function e(t){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.post("/api/scraping/stop/".concat(t)))},e,this)})),function(e){return m.apply(this,arguments)})},{key:"getScrapingResults",value:(u=be(de().m(function e(t){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.get("/api/scraping/results/".concat(t)))},e,this)})),function(e){return u.apply(this,arguments)})},{key:"exportScrapingResults",value:(s=be(de().m(function e(t){var n,r=arguments;return de().w(function(e){for(;;)if(0===e.n)return n=r.length>1&&void 0!==r[1]?r[1]:"excel",e.a(2,this.get("/api/scraping/export/".concat(t,"?format=").concat(n),{responseType:"blob"}))},e,this)})),function(e){return s.apply(this,arguments)})},{key:"startMessaging",value:(c=be(de().m(function e(t){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.post("/api/messaging/start",t))},e,this)})),function(e){return c.apply(this,arguments)})},{key:"getMessagingStatus",value:(l=be(de().m(function e(t){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.get("/api/messaging/status/".concat(t)))},e,this)})),function(e){return l.apply(this,arguments)})},{key:"stopMessaging",value:(i=be(de().m(function e(t){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.post("/api/messaging/stop/".concat(t)))},e,this)})),function(e){return i.apply(this,arguments)})},{key:"getMessagingResults",value:(o=be(de().m(function e(t){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.get("/api/messaging/results/".concat(t)))},e,this)})),function(e){return o.apply(this,arguments)})},{key:"uploadRecipientList",value:(a=be(de().m(function e(t){var n;return de().w(function(e){for(;;)if(0===e.n)return(n=new FormData).append("file",t),e.a(2,this.post("/api/messaging/upload-recipients",n,{headers:{"Content-Type":"multipart/form-data"}}))},e,this)})),function(e){return a.apply(this,arguments)})},{key:"getSystemStatus",value:(r=be(de().m(function e(){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.get("/health"))},e,this)})),function(){return r.apply(this,arguments)})},{key:"getSystemStats",value:(n=be(de().m(function e(){return de().w(function(e){for(;;)if(0===e.n)return e.a(2,this.get("/api/system/stats"))},e,this)})),function(){return n.apply(this,arguments)})}],t&&Ee(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,a,o,i,l,c,s,u,m,p,A,f,d,g,y,b,E,h,x,v,C,k}());function ve(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function o(n,r,a,o){var c=r&&r.prototype instanceof l?r:l,s=Object.create(c.prototype);return Ce(s,"_invoke",function(n,r,a){var o,l,c,s=0,u=a||[],m=!1,p={p:0,n:0,v:e,a:A,f:A.bind(e,4),d:function(t,n){return o=t,l=0,c=e,p.n=n,i}};function A(n,r){for(l=n,c=r,t=0;!m&&s&&!a&&t<u.length;t++){var a,o=u[t],A=p.p,f=o[2];n>3?(a=f===r)&&(c=o[(l=o[4])?5:(l=3,3)],o[4]=o[5]=e):o[0]<=A&&((a=n<2&&A<o[1])?(l=0,p.v=r,p.n=o[1]):A<f&&(a=n<3||o[0]>r||r>f)&&(o[4]=n,o[5]=r,p.n=f,l=0))}if(a||n>1)return i;throw m=!0,r}return function(a,u,f){if(s>1)throw TypeError("Generator is already running");for(m&&1===u&&A(u,f),l=u,c=f;(t=l<2?e:c)||!m;){o||(l?l<3?(l>1&&(p.n=-1),A(l,c)):p.n=c:p.v=c);try{if(s=2,o){if(l||(a="next"),t=o[a]){if(!(t=t.call(o,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,l<2&&(l=0)}else 1===l&&(t=o.return)&&t.call(o),l<2&&(c=TypeError("The iterator does not provide a '"+a+"' method"),l=1);o=e}else if((t=(m=p.n<0)?c:n.call(r,p))!==i)break}catch(t){o=e,l=1,c=t}finally{s=1}}return{value:t,done:m}}}(n,a,o),!0),s}var i={};function l(){}function c(){}function s(){}t=Object.getPrototypeOf;var u=[][r]?t(t([][r]())):(Ce(t={},r,function(){return this}),t),m=s.prototype=l.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,Ce(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e}return c.prototype=s,Ce(m,"constructor",s),Ce(s,"constructor",c),c.displayName="GeneratorFunction",Ce(s,a,"GeneratorFunction"),Ce(m),Ce(m,a,"Generator"),Ce(m,r,function(){return this}),Ce(m,"toString",function(){return"[object Generator]"}),(ve=function(){return{w:o,m:p}})()}function Ce(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}Ce=function(e,t,n,r){if(t)a?a(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var o=function(t,n){Ce(e,t,function(e){return this._invoke(t,n,e)})};o("next",0),o("throw",1),o("return",2)}},Ce(e,t,n,r)}function ke(e){return function(e){if(Array.isArray(e))return _e(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Se(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function we(e,t,n,r,a,o,i){try{var l=e[o](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,a)}function Be(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||Se(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Se(e,t){if(e){if("string"==typeof e)return _e(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_e(e,t):void 0}}function _e(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var Pe=Y.A.Title,Ie=Y.A.Text;const Oe=function(){var e=Be((0,r.useState)(!0),2),t=e[0],n=e[1],a=Be((0,r.useState)({profiles:{total:0,active:0,logged_in:0},scraping:{total_tasks:0,completed:0,running:0},messaging:{total_tasks:0,messages_sent:0,success_rate:0},system:{uptime:0,memory_usage:0,cpu_usage:0}}),2),o=a[0],i=a[1],s=Be((0,r.useState)([]),2),u=s[0],m=s[1],p=Be((0,r.useState)("healthy"),2),f=p[0],g=p[1],b=Be((0,r.useState)(new Date),2),E=b[0],k=b[1];(0,r.useEffect)(function(){w();var e=setInterval(w,3e4),t=setInterval(function(){k(new Date)},1e3);return function(){clearInterval(e),clearInterval(t)}},[]);var w=function(){var e,t=(e=ve().m(function e(){var t,r,a,o,c,s,u,p,A,f,d;return ve().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,n(!0),e.n=1,Promise.all([xe.getProfiles().catch(function(){return[]}),xe.get("/api/scraping/").catch(function(){return[]}),xe.get("/api/messaging/").catch(function(){return[]}),xe.get("/api/system/stats").catch(function(){return{performance:{}}})]);case 1:t=e.v,r=Be(t,4),a=r[0],o=r[1],c=r[2],s=r[3],u={total:a.length,active:a.filter(function(e){return"active"===e.status}).length,logged_in:a.filter(function(e){return e.facebook_logged_in}).length},p={total_tasks:o.length,completed:o.filter(function(e){return"completed"===e.status}).length,running:o.filter(function(e){return"running"===e.status}).length},A={total_tasks:c.length,messages_sent:c.reduce(function(e,t){return e+(t.messages_sent||0)},0),success_rate:c.length>0?c.reduce(function(e,t){return e+(t.messages_sent||0)},0)/c.reduce(function(e,t){return e+(t.total_recipients||1)},0)*100:0},i({profiles:u,scraping:p,messaging:A,system:s.performance||{}}),f=[].concat(ke(o.slice(0,3).map(function(e){return{type:"scraping",title:"Scraping task completed",description:"Found ".concat(e.total_found||0," users from Facebook post"),time:e.completed_at||e.created_at,status:e.status}})),ke(c.slice(0,3).map(function(e){return{type:"messaging",title:"Messaging campaign finished",description:"Sent ".concat(e.messages_sent||0," messages to recipients"),time:e.completed_at||e.created_at,status:e.status}}))).sort(function(e,t){return new Date(t.time)-new Date(e.time)}).slice(0,5),m(f),g("healthy"),e.n=3;break;case 2:e.p=2,d=e.v,console.error("Failed to load dashboard data:",d),g("error"),l.Ay.error({message:"Dashboard Error",description:"Failed to load dashboard data. Please check your connection.",duration:4});case 3:return e.p=3,n(!1),e.f(3);case 4:return e.a(2)}},e,null,[[0,2,3,4]])}),function(){var t=this,n=arguments;return new Promise(function(r,a){var o=e.apply(t,n);function i(e){we(o,r,a,i,l,"next",e)}function l(e){we(o,r,a,i,l,"throw",e)}i(void 0)})});return function(){return t.apply(this,arguments)}}(),B=function(e){switch(e){case"completed":return"success";case"running":return"processing";case"failed":return"error";default:return"default"}},S=function(e){switch(e){case"scraping":return r.createElement(x.A,{style:{color:"#1890ff"}});case"messaging":return r.createElement(v.A,{style:{color:"#52c41a"}});default:return r.createElement(re.A,null)}};return t?r.createElement("div",{className:"loading-container",style:{display:"flex",justifyContent:"center",alignItems:"center",height:"60vh",flexDirection:"column"}},r.createElement(c.A,{size:"large"}),r.createElement(Ie,{style:{marginTop:"16px",fontSize:"16px"}},"Loading dashboard...")):r.createElement("div",{className:"modern-dashboard fade-in"},r.createElement("div",{className:"dashboard-hero",style:{marginBottom:"32px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",borderRadius:"20px",padding:"32px",color:"white",position:"relative",overflow:"hidden"}},r.createElement("div",{style:{position:"absolute",top:0,right:0,width:"200px",height:"200px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",transform:"translate(50%, -50%)"}}),r.createElement(H.A,{justify:"space-between",align:"middle"},r.createElement(V.A,null,r.createElement(K.A,{direction:"vertical",size:"small"},r.createElement(Pe,{level:1,style:{color:"white",margin:0,fontSize:"36px"}},"🚀 Facebook Automation"),r.createElement(Ie,{style:{color:"rgba(255,255,255,0.9)",fontSize:"18px"}},"Complete automation solution for Facebook marketing"),r.createElement("div",{style:{marginTop:"12px",fontSize:"14px",opacity:.8}},r.createElement(re.A,{style:{marginRight:"8px"}}),E.toLocaleString()))),r.createElement(V.A,null,r.createElement(K.A,{direction:"vertical",align:"end"},r.createElement(d.A,{status:"processing",text:"System Online",style:{color:"white",fontSize:"16px"}}),r.createElement(A.Ay,{icon:r.createElement(ae.A,null),onClick:w,loading:t,size:"large",style:{background:"rgba(255,255,255,0.2)",border:"none",color:"white",borderRadius:"12px",backdropFilter:"blur(10px)"}},"Refresh Data"))))),"healthy"!==f&&r.createElement(Z.A,{message:"System Status Warning",description:"Some services may be unavailable. Please check your backend connection.",type:"warning",showIcon:!0,style:{marginBottom:"24px",borderRadius:"12px"},action:r.createElement(A.Ay,{size:"small",onClick:w},"Retry Connection")}),r.createElement(H.A,{gutter:[24,24],style:{marginBottom:"32px"}},r.createElement(V.A,{xs:24,sm:12,lg:6},r.createElement(X.A,{className:"custom-card bounce-in",style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none",color:"white",borderRadius:"16px",overflow:"hidden"}},r.createElement("div",{style:{position:"relative"}},r.createElement(h.A,{style:{position:"absolute",top:"-10px",right:"-10px",fontSize:"60px",opacity:.2}}),r.createElement(Q.A,{title:r.createElement("span",{style:{color:"rgba(255,255,255,0.9)",fontSize:"14px"}},"Browser Profiles"),value:o.profiles.total,valueStyle:{color:"white",fontSize:"32px",fontWeight:"bold"}}),r.createElement("div",{style:{marginTop:"12px",fontSize:"14px",opacity:.9}},r.createElement(oe.A,{style:{marginRight:"6px"}}),o.profiles.logged_in," logged into Facebook"),r.createElement($.A,{percent:o.profiles.total>0?o.profiles.logged_in/o.profiles.total*100:0,size:"small",showInfo:!1,strokeColor:"rgba(255,255,255,0.8)",trailColor:"rgba(255,255,255,0.2)",style:{marginTop:"12px"}})))),r.createElement(V.A,{xs:24,sm:12,lg:6},r.createElement(X.A,{className:"custom-card bounce-in",style:{background:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",border:"none",color:"white",borderRadius:"16px",overflow:"hidden"}},r.createElement("div",{style:{position:"relative"}},r.createElement(x.A,{style:{position:"absolute",top:"-10px",right:"-10px",fontSize:"60px",opacity:.2}}),r.createElement(Q.A,{title:r.createElement("span",{style:{color:"rgba(255,255,255,0.9)",fontSize:"14px"}},"Scraping Tasks"),value:o.scraping.total_tasks,valueStyle:{color:"white",fontSize:"32px",fontWeight:"bold"}}),r.createElement("div",{style:{marginTop:"12px",fontSize:"14px",opacity:.9}},r.createElement(ie.A,{style:{marginRight:"6px"}}),o.scraping.completed," completed successfully"),r.createElement($.A,{percent:o.scraping.total_tasks>0?o.scraping.completed/o.scraping.total_tasks*100:0,size:"small",showInfo:!1,strokeColor:"rgba(255,255,255,0.8)",trailColor:"rgba(255,255,255,0.2)",style:{marginTop:"12px"}})))),r.createElement(V.A,{xs:24,sm:12,lg:6},r.createElement(X.A,{className:"custom-card bounce-in",style:{background:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",border:"none",color:"white",borderRadius:"16px",overflow:"hidden"}},r.createElement("div",{style:{position:"relative"}},r.createElement(v.A,{style:{position:"absolute",top:"-10px",right:"-10px",fontSize:"60px",opacity:.2}}),r.createElement(Q.A,{title:r.createElement("span",{style:{color:"rgba(255,255,255,0.9)",fontSize:"14px"}},"Messages Sent"),value:o.messaging.messages_sent,valueStyle:{color:"white",fontSize:"32px",fontWeight:"bold"}}),r.createElement("div",{style:{marginTop:"12px",fontSize:"14px",opacity:.9}},r.createElement(le.A,{style:{marginRight:"6px"}}),o.messaging.total_tasks," campaigns completed"),r.createElement($.A,{percent:Math.min(o.messaging.success_rate,100),size:"small",showInfo:!1,strokeColor:"rgba(255,255,255,0.8)",trailColor:"rgba(255,255,255,0.2)",style:{marginTop:"12px"}})))),r.createElement(V.A,{xs:24,sm:12,lg:6},r.createElement(X.A,{className:"custom-card bounce-in",style:{background:"linear-gradient(135deg, #fa709a 0%, #fee140 100%)",border:"none",color:"white",borderRadius:"16px",overflow:"hidden"}},r.createElement("div",{style:{position:"relative"}},r.createElement(ce.A,{style:{position:"absolute",top:"-10px",right:"-10px",fontSize:"60px",opacity:.2}}),r.createElement(Q.A,{title:r.createElement("span",{style:{color:"rgba(255,255,255,0.9)",fontSize:"14px"}},"Success Rate"),value:o.messaging.success_rate,precision:1,suffix:"%",valueStyle:{color:"white",fontSize:"32px",fontWeight:"bold"}}),r.createElement("div",{style:{marginTop:"12px",fontSize:"14px",opacity:.9}},r.createElement(P.A,{style:{marginRight:"6px"}}),"Average campaign performance"),r.createElement($.A,{percent:Math.min(o.messaging.success_rate,100),size:"small",showInfo:!1,strokeColor:"rgba(255,255,255,0.8)",trailColor:"rgba(255,255,255,0.2)",style:{marginTop:"12px"}}))))),r.createElement(H.A,{gutter:[24,24]},r.createElement(V.A,{xs:24,lg:8},r.createElement(X.A,{title:r.createElement(K.A,null,r.createElement(se.A,{style:{color:"#667eea"}}),r.createElement("span",null,"Quick Actions")),className:"custom-card slide-up",style:{marginBottom:"24px",borderRadius:"16px"}},r.createElement(K.A,{direction:"vertical",style:{width:"100%"},size:"middle"},r.createElement(A.Ay,{type:"primary",block:!0,size:"large",icon:r.createElement(h.A,null),onClick:function(){return window.location.hash="/profiles"},className:"gradient-button",style:{height:"50px",borderRadius:"12px"}},"Manage Profiles"),r.createElement(A.Ay,{type:"primary",block:!0,size:"large",icon:r.createElement(x.A,null),onClick:function(){return window.location.hash="/scraping"},className:"gradient-button",style:{height:"50px",borderRadius:"12px"}},"Start Scraping"),r.createElement(A.Ay,{type:"primary",block:!0,size:"large",icon:r.createElement(v.A,null),onClick:function(){return window.location.hash="/messaging"},className:"gradient-button",style:{height:"50px",borderRadius:"12px"}},"Send Messages"),r.createElement(A.Ay,{type:"default",block:!0,size:"large",icon:r.createElement(C.A,null),onClick:function(){return window.location.hash="/settings"},style:{height:"50px",borderRadius:"12px"}},"Settings"))),r.createElement(X.A,{title:r.createElement(K.A,null,r.createElement(ue.A,{style:{color:"#52c41a"}}),r.createElement("span",null,"System Status")),className:"custom-card slide-up",style:{borderRadius:"16px"}},r.createElement(J.A,{items:[{dot:r.createElement(oe.A,{style:{color:"#52c41a",fontSize:"16px"}}),children:r.createElement("div",null,r.createElement("div",{style:{fontWeight:"bold",marginBottom:"4px"}},"Backend Service"),r.createElement("div",{style:{color:"#52c41a",fontSize:"14px"}},"Connected and running"))},{dot:r.createElement(oe.A,{style:{color:"#52c41a",fontSize:"16px"}}),children:r.createElement("div",null,r.createElement("div",{style:{fontWeight:"bold",marginBottom:"4px"}},"Database"),r.createElement("div",{style:{color:"#52c41a",fontSize:"14px"}},"Operational"))},{dot:r.createElement(oe.A,{style:{color:"#52c41a",fontSize:"16px"}}),children:r.createElement("div",null,r.createElement("div",{style:{fontWeight:"bold",marginBottom:"4px"}},"Browser Engine"),r.createElement("div",{style:{color:"#52c41a",fontSize:"14px"}},"Ready for automation"))}]}))),r.createElement(V.A,{xs:24,lg:16},r.createElement(X.A,{title:r.createElement(K.A,null,r.createElement(me.A,{style:{color:"#1890ff"}}),r.createElement("span",null,"Recent Activities")),className:"custom-card slide-up",style:{borderRadius:"16px"},extra:r.createElement(A.Ay,{type:"link",icon:r.createElement(pe.A,null),onClick:function(){return window.location.hash="/scraping"}},"Start New Task")},u.length>0?r.createElement(ee.A,{itemLayout:"horizontal",dataSource:u,renderItem:function(e){return r.createElement(ee.A.Item,{style:{padding:"16px 0",borderBottom:"1px solid #f0f0f0"}},r.createElement(ee.A.Item.Meta,{avatar:r.createElement(y.A,{icon:S(e.type),style:{background:"scraping"===e.type?"#e6f7ff":"#f6ffed",border:"2px solid ".concat("scraping"===e.type?"#1890ff":"#52c41a")}}),title:r.createElement(K.A,null,r.createElement("span",{style:{fontWeight:"bold"}},e.title),r.createElement(te.A,{color:B(e.status)},e.status)),description:r.createElement("div",null,r.createElement("div",{style:{marginBottom:"4px"}},e.description),r.createElement("div",{style:{fontSize:"12px",color:"#999"}},r.createElement(re.A,{style:{marginRight:"4px"}}),new Date(e.time).toLocaleString()))}))}}):r.createElement(ne.A,{description:"No recent activities",image:ne.A.PRESENTED_IMAGE_SIMPLE,style:{padding:"40px 0"}})))))};var je=n(9041),ze=n(3691),Te=n(9036),Fe=n(6044),De=n(9361),Re=n(6349),Me=n(9795),Ue=n(6555),Ne=n(261),Le=n(3598),qe=n(9237);function We(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function o(n,r,a,o){var c=r&&r.prototype instanceof l?r:l,s=Object.create(c.prototype);return Ge(s,"_invoke",function(n,r,a){var o,l,c,s=0,u=a||[],m=!1,p={p:0,n:0,v:e,a:A,f:A.bind(e,4),d:function(t,n){return o=t,l=0,c=e,p.n=n,i}};function A(n,r){for(l=n,c=r,t=0;!m&&s&&!a&&t<u.length;t++){var a,o=u[t],A=p.p,f=o[2];n>3?(a=f===r)&&(c=o[(l=o[4])?5:(l=3,3)],o[4]=o[5]=e):o[0]<=A&&((a=n<2&&A<o[1])?(l=0,p.v=r,p.n=o[1]):A<f&&(a=n<3||o[0]>r||r>f)&&(o[4]=n,o[5]=r,p.n=f,l=0))}if(a||n>1)return i;throw m=!0,r}return function(a,u,f){if(s>1)throw TypeError("Generator is already running");for(m&&1===u&&A(u,f),l=u,c=f;(t=l<2?e:c)||!m;){o||(l?l<3?(l>1&&(p.n=-1),A(l,c)):p.n=c:p.v=c);try{if(s=2,o){if(l||(a="next"),t=o[a]){if(!(t=t.call(o,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,l<2&&(l=0)}else 1===l&&(t=o.return)&&t.call(o),l<2&&(c=TypeError("The iterator does not provide a '"+a+"' method"),l=1);o=e}else if((t=(m=p.n<0)?c:n.call(r,p))!==i)break}catch(t){o=e,l=1,c=t}finally{s=1}}return{value:t,done:m}}}(n,a,o),!0),s}var i={};function l(){}function c(){}function s(){}t=Object.getPrototypeOf;var u=[][r]?t(t([][r]())):(Ge(t={},r,function(){return this}),t),m=s.prototype=l.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,Ge(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e}return c.prototype=s,Ge(m,"constructor",s),Ge(s,"constructor",c),c.displayName="GeneratorFunction",Ge(s,a,"GeneratorFunction"),Ge(m),Ge(m,a,"Generator"),Ge(m,r,function(){return this}),Ge(m,"toString",function(){return"[object Generator]"}),(We=function(){return{w:o,m:p}})()}function Ge(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}Ge=function(e,t,n,r){if(t)a?a(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var o=function(t,n){Ge(e,t,function(e){return this._invoke(t,n,e)})};o("next",0),o("throw",1),o("return",2)}},Ge(e,t,n,r)}function Ye(e,t,n,r,a,o,i){try{var l=e[o](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,a)}function He(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var o=e.apply(t,n);function i(e){Ye(o,r,a,i,l,"next",e)}function l(e){Ye(o,r,a,i,l,"throw",e)}i(void 0)})}}function Ve(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Ke(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ke(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ke(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var Ze=je.A.Option;const Xe=function(){var e=Ve((0,r.useState)([]),2),t=e[0],n=e[1],a=Ve((0,r.useState)(!1),2),o=a[0],i=a[1],l=Ve((0,r.useState)(!1),2),c=l[0],s=l[1],u=Ve((0,r.useState)(null),2),m=u[0],p=u[1],f=Ve((0,r.useState)(!1),2),d=f[0],g=f[1],y=Ve((0,r.useState)(null),2),E=y[0],h=y[1],x=Ve(ze.A.useForm(),1)[0],v=Ve(ze.A.useForm(),1)[0];(0,r.useEffect)(function(){C()},[]);var C=function(){var e=He(We().m(function e(){var t,r;return We().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,i(!0),e.n=1,xe.getProfiles();case 1:t=e.v,n(t),e.n=3;break;case 2:e.p=2,r=e.v,Te.Ay.error("Failed to load profiles: "+r.message);case 3:return e.p=3,i(!1),e.f(3);case 4:return e.a(2)}},e,null,[[0,2,3,4]])}));return function(){return e.apply(this,arguments)}}(),k=function(){var e=He(We().m(function e(t){var n,r;return We().w(function(e){for(;;)switch(e.n){case 0:if(e.p=0,n={name:t.name,proxy_config:{type:t.proxy_type||"no_proxy",host:t.proxy_host,port:t.proxy_port,username:t.proxy_username,password:t.proxy_password}},!m){e.n=2;break}return e.n=1,xe.updateProfile(m.id,n);case 1:Te.Ay.success("Profile updated successfully"),e.n=4;break;case 2:return e.n=3,xe.createProfile(n);case 3:Te.Ay.success("Profile created successfully");case 4:s(!1),C(),e.n=6;break;case 5:e.p=5,r=e.v,Te.Ay.error("Failed to save profile: "+r.message);case 6:return e.a(2)}},e,null,[[0,5]])}));return function(t){return e.apply(this,arguments)}}(),w=function(){var e=He(We().m(function e(t){var n;return We().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,xe.deleteProfile(t);case 1:Te.Ay.success("Profile deleted successfully"),C(),e.n=3;break;case 2:e.p=2,n=e.v,Te.Ay.error("Failed to delete profile: "+n.message);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}(),B=function(){var e=He(We().m(function e(t){var n,r;return We().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,Te.Ay.loading("Testing profile...",0),e.n=1,xe.testProfile(t.id);case 1:n=e.v,Te.Ay.destroy(),n.success?Te.Ay.success("Profile test successful! IP: ".concat(n.ip_address)):Te.Ay.error("Profile test failed: ".concat(n.message)),C(),e.n=3;break;case 2:e.p=2,r=e.v,Te.Ay.destroy(),Te.Ay.error("Failed to test profile: "+r.message);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}(),S=function(){var e=He(We().m(function e(t){var n,r;return We().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,xe.loginFacebook(E.id,t);case 1:(n=e.v).success?n.manual_login_required?Te.Ay.info("Facebook login page opened. Please login manually in the browser."):Te.Ay.success("Facebook login successful"):Te.Ay.error("Facebook login failed: ".concat(n.message)),g(!1),C(),e.n=3;break;case 2:e.p=2,r=e.v,Te.Ay.error("Failed to login Facebook: "+r.message);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}(),_=[{title:"Name",dataIndex:"name",key:"name",sorter:function(e,t){return e.name.localeCompare(t.name)}},{title:"Proxy",key:"proxy",render:function(e,t){var n=t.proxy_config;return"no_proxy"===n.type?r.createElement(te.A,{color:"default"},"No Proxy"):r.createElement(b.A,{title:"".concat(n.host,":").concat(n.port)},r.createElement(te.A,{color:"blue"},n.type.toUpperCase()))}},{title:"Facebook Status",key:"facebook_status",render:function(e,t){return t.facebook_logged_in?r.createElement(te.A,{color:"success"},"Logged In (",t.facebook_username,")"):r.createElement(te.A,{color:"default"},"Not Logged In")}},{title:"Status",dataIndex:"status",key:"status",render:function(e){return function(e){var t={created:{color:"default",text:"Created"},active:{color:"processing",text:"Active"},logged_in:{color:"success",text:"Logged In"},error:{color:"error",text:"Error"},disabled:{color:"default",text:"Disabled"}},n=t[e]||t.created;return r.createElement(te.A,{color:n.color},n.text)}(e)},filters:[{text:"Created",value:"created"},{text:"Active",value:"active"},{text:"Logged In",value:"logged_in"},{text:"Error",value:"error"}],onFilter:function(e,t){return t.status===e}},{title:"Last Used",dataIndex:"last_used",key:"last_used",render:function(e){return e?new Date(e).toLocaleString():"Never"},sorter:function(e,t){return new Date(e.last_used||0)-new Date(t.last_used||0)}},{title:"Actions",key:"actions",render:function(e,t){return r.createElement(K.A,{size:"small"},r.createElement(b.A,{title:"Test Profile"},r.createElement(A.Ay,{icon:r.createElement(pe.A,null),size:"small",onClick:function(){return B(t)}})),r.createElement(b.A,{title:"Facebook Login"},r.createElement(A.Ay,{icon:r.createElement(Ue.A,null),size:"small",type:t.facebook_logged_in?"default":"primary",onClick:function(){return h(t),g(!0),void v.resetFields()}})),r.createElement(b.A,{title:"Edit Profile"},r.createElement(A.Ay,{icon:r.createElement(Ne.A,null),size:"small",onClick:function(){return p(e=t),s(!0),void x.setFieldsValue({name:e.name,proxy_type:e.proxy_config.type,proxy_host:e.proxy_config.host,proxy_port:e.proxy_config.port,proxy_username:e.proxy_config.username,proxy_password:e.proxy_config.password});var e}})),r.createElement(b.A,{title:"Delete Profile"},r.createElement(Fe.A,{title:"Are you sure you want to delete this profile?",onConfirm:function(){return w(t.id)},okText:"Yes",cancelText:"No"},r.createElement(A.Ay,{icon:r.createElement(Le.A,null),size:"small",danger:!0}))))}}];return r.createElement("div",null,r.createElement(H.A,{justify:"space-between",align:"middle",style:{marginBottom:16}},r.createElement(V.A,null,r.createElement("h1",null,"Profile Manager")),r.createElement(V.A,null,r.createElement(K.A,null,r.createElement(A.Ay,{icon:r.createElement(ae.A,null),onClick:C,loading:o},"Refresh"),r.createElement(A.Ay,{type:"primary",icon:r.createElement(qe.A,null),onClick:function(){p(null),s(!0),x.resetFields()}},"Create Profile")))),r.createElement(X.A,null,r.createElement(De.A,{columns:_,dataSource:t,rowKey:"id",loading:o,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e){return"Total ".concat(e," profiles")}}})),r.createElement(Re.A,{title:m?"Edit Profile":"Create Profile",open:c,onCancel:function(){return s(!1)},footer:null,width:600},r.createElement(ze.A,{form:x,layout:"vertical",onFinish:k},r.createElement(ze.A.Item,{name:"name",label:"Profile Name",rules:[{required:!0,message:"Please enter profile name"}]},r.createElement(z.A,{placeholder:"Enter profile name"})),r.createElement(X.A,{title:"Proxy Settings",size:"small",style:{marginBottom:16}},r.createElement(ze.A.Item,{name:"proxy_type",label:"Proxy Type",initialValue:"no_proxy"},r.createElement(je.A,null,r.createElement(Ze,{value:"no_proxy"},"No Proxy (Local Network)"),r.createElement(Ze,{value:"http"},"HTTP"),r.createElement(Ze,{value:"https"},"HTTPS"),r.createElement(Ze,{value:"socks5"},"SOCKS5"),r.createElement(Ze,{value:"ssh"},"SSH"))),r.createElement(ze.A.Item,{noStyle:!0,shouldUpdate:function(e,t){return e.proxy_type!==t.proxy_type}},function(e){return"no_proxy"===(0,e.getFieldValue)("proxy_type")?null:r.createElement(r.Fragment,null,r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:16},r.createElement(ze.A.Item,{name:"proxy_host",label:"Host",rules:[{required:!0,message:"Please enter proxy host"}]},r.createElement(z.A,{placeholder:"proxy.example.com"}))),r.createElement(V.A,{span:8},r.createElement(ze.A.Item,{name:"proxy_port",label:"Port",rules:[{required:!0,message:"Please enter proxy port"}]},r.createElement(Me.A,{placeholder:"8080",min:1,max:65535,style:{width:"100%"}})))),r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"proxy_username",label:"Username"},r.createElement(z.A,{placeholder:"Optional"}))),r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"proxy_password",label:"Password"},r.createElement(z.A.Password,{placeholder:"Optional"})))))})),r.createElement(ze.A.Item,null,r.createElement(K.A,null,r.createElement(A.Ay,{type:"primary",htmlType:"submit"},m?"Update":"Create"," Profile"),r.createElement(A.Ay,{onClick:function(){return s(!1)}},"Cancel"))))),r.createElement(Re.A,{title:"Facebook Login",open:d,onCancel:function(){return g(!1)},footer:null},r.createElement(ze.A,{form:v,layout:"vertical",onFinish:S},r.createElement(ze.A.Item,{name:"username",label:"Facebook Username/Email",rules:[{required:!0,message:"Please enter Facebook username"}]},r.createElement(z.A,{placeholder:"Enter Facebook username or email"})),r.createElement(ze.A.Item,{name:"password",label:"Facebook Password",rules:[{required:!0,message:"Please enter Facebook password"}]},r.createElement(z.A.Password,{placeholder:"Enter Facebook password"})),r.createElement(ze.A.Item,null,r.createElement(K.A,null,r.createElement(A.Ay,{type:"primary",htmlType:"submit"},"Login"),r.createElement(A.Ay,{onClick:function(){return g(!1)}},"Cancel"))))))};var Qe=n(1196),$e=n(4581),Je=n(234),et=n(1952);function tt(e){return tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},tt(e)}function nt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function rt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?nt(Object(n),!0).forEach(function(t){at(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):nt(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function at(e,t,n){return(t=function(e){var t=function(e){if("object"!=tt(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=tt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==tt(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ot(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function o(n,r,a,o){var c=r&&r.prototype instanceof l?r:l,s=Object.create(c.prototype);return it(s,"_invoke",function(n,r,a){var o,l,c,s=0,u=a||[],m=!1,p={p:0,n:0,v:e,a:A,f:A.bind(e,4),d:function(t,n){return o=t,l=0,c=e,p.n=n,i}};function A(n,r){for(l=n,c=r,t=0;!m&&s&&!a&&t<u.length;t++){var a,o=u[t],A=p.p,f=o[2];n>3?(a=f===r)&&(c=o[(l=o[4])?5:(l=3,3)],o[4]=o[5]=e):o[0]<=A&&((a=n<2&&A<o[1])?(l=0,p.v=r,p.n=o[1]):A<f&&(a=n<3||o[0]>r||r>f)&&(o[4]=n,o[5]=r,p.n=f,l=0))}if(a||n>1)return i;throw m=!0,r}return function(a,u,f){if(s>1)throw TypeError("Generator is already running");for(m&&1===u&&A(u,f),l=u,c=f;(t=l<2?e:c)||!m;){o||(l?l<3?(l>1&&(p.n=-1),A(l,c)):p.n=c:p.v=c);try{if(s=2,o){if(l||(a="next"),t=o[a]){if(!(t=t.call(o,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,l<2&&(l=0)}else 1===l&&(t=o.return)&&t.call(o),l<2&&(c=TypeError("The iterator does not provide a '"+a+"' method"),l=1);o=e}else if((t=(m=p.n<0)?c:n.call(r,p))!==i)break}catch(t){o=e,l=1,c=t}finally{s=1}}return{value:t,done:m}}}(n,a,o),!0),s}var i={};function l(){}function c(){}function s(){}t=Object.getPrototypeOf;var u=[][r]?t(t([][r]())):(it(t={},r,function(){return this}),t),m=s.prototype=l.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,it(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e}return c.prototype=s,it(m,"constructor",s),it(s,"constructor",c),c.displayName="GeneratorFunction",it(s,a,"GeneratorFunction"),it(m),it(m,a,"Generator"),it(m,r,function(){return this}),it(m,"toString",function(){return"[object Generator]"}),(ot=function(){return{w:o,m:p}})()}function it(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}it=function(e,t,n,r){if(t)a?a(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var o=function(t,n){it(e,t,function(e){return this._invoke(t,n,e)})};o("next",0),o("throw",1),o("return",2)}},it(e,t,n,r)}function lt(e,t,n,r,a,o,i){try{var l=e[o](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,a)}function ct(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var o=e.apply(t,n);function i(e){lt(o,r,a,i,l,"next",e)}function l(e){lt(o,r,a,i,l,"throw",e)}i(void 0)})}}function st(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return ut(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ut(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ut(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var mt=je.A.Option;const pt=function(){var e=st(ze.A.useForm(),1)[0],t=st((0,r.useState)(!1),2),n=t[0],a=t[1],o=st((0,r.useState)([]),2),i=o[0],l=o[1],c=st((0,r.useState)([]),2),s=c[0],u=c[1],m=st((0,r.useState)(null),2),p=m[0],f=m[1],d=st((0,r.useState)({}),2),g=d[0],y=d[1],E=st((0,r.useState)(!1),2),v=E[0],C=E[1],k=st((0,r.useState)(null),2),w=k[0],B=k[1],S=st((0,r.useState)([]),2),_=(S[0],S[1]);(0,r.useEffect)(function(){P();var e=setInterval(function(){p&&O(p)},2e3);return function(){return clearInterval(e)}},[p]);var P=function(){var e=ct(ot().m(function e(){var t,n,r,o,i,c;return ot().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,a(!0),e.n=1,Promise.all([xe.getProfiles(),xe.get("/api/scraping/"),xe.get("/api/scraping/exports/history").catch(function(){return{exports:[]}})]);case 1:t=e.v,n=st(t,3),r=n[0],o=n[1],i=n[2],u(r.filter(function(e){return e.facebook_logged_in})),l(o),_(i.exports||[]),e.n=3;break;case 2:e.p=2,c=e.v,Te.Ay.error("Failed to load data: "+c.message);case 3:return e.p=3,a(!1),e.f(3);case 4:return e.a(2)}},e,null,[[0,2,3,4]])}));return function(){return e.apply(this,arguments)}}(),I=function(){var t=ct(ot().m(function t(n){var r,o,i;return ot().w(function(t){for(;;)switch(t.n){case 0:return t.p=0,a(!0),r={target_url:n.target_url,scraping_types:n.scraping_types||["all"],max_results:n.max_results||1e3,profile_id:n.profile_id},t.n=1,xe.post("/api/scraping/start",{config:r});case 1:o=t.v,f(o.task_id),Te.Ay.success("Scraping task started successfully"),e.resetFields(),P(),t.n=3;break;case 2:t.p=2,i=t.v,Te.Ay.error("Failed to start scraping: "+i.message);case 3:return t.p=3,a(!1),t.f(3);case 4:return t.a(2)}},t,null,[[0,2,3,4]])}));return function(e){return t.apply(this,arguments)}}(),O=function(){var e=ct(ot().m(function e(t){var n,r;return ot().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,xe.get("/api/scraping/status/".concat(t));case 1:n=e.v,y(function(e){return rt(rt({},e),{},at({},t,n))}),["completed","failed","cancelled"].includes(n.status)&&(f(null),P()),e.n=3;break;case 2:e.p=2,r=e.v,console.error("Failed to poll task status:",r);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}(),j=function(){var e=ct(ot().m(function e(t){var n;return ot().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,xe.post("/api/scraping/stop/".concat(t));case 1:Te.Ay.success("Task stopped successfully"),f(null),P(),e.n=3;break;case 2:e.p=2,n=e.v,Te.Ay.error("Failed to stop task: "+n.message);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}(),T=function(){var e=ct(ot().m(function e(t){var n,r;return ot().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,xe.get("/api/scraping/results/".concat(t));case 1:n=e.v,B(n),C(!0),e.n=3;break;case 2:e.p=2,r=e.v,Te.Ay.error("Failed to load results: "+r.message);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}(),F=function(){var e=ct(ot().m(function e(t){var n,r,a,o=arguments;return ot().w(function(e){for(;;)switch(e.n){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:"excel",e.p=1,e.n=2,xe.get("/api/scraping/export/".concat(t,"?format=").concat(n));case 2:(r=e.v).success?(Te.Ay.success("Data exported successfully to ".concat(r.filename)),P()):Te.Ay.error("Export failed: "+r.error),e.n=4;break;case 3:e.p=3,a=e.v,Te.Ay.error("Failed to export results: "+a.message);case 4:return e.a(2)}},e,null,[[1,3]])}));return function(t){return e.apply(this,arguments)}}(),D=function(e){var t={pending:{color:"default",text:"Pending"},running:{color:"processing",text:"Running"},completed:{color:"success",text:"Completed"},failed:{color:"error",text:"Failed"},cancelled:{color:"default",text:"Cancelled"}},n=t[e]||t.pending;return r.createElement(te.A,{color:n.color},n.text)},R=[{title:"Target URL",dataIndex:"target_url",key:"target_url",render:function(e){return r.createElement(b.A,{title:e},r.createElement("a",{href:e,target:"_blank",rel:"noopener noreferrer"},e.length>50?e.substring(0,50)+"...":e))}},{title:"Status",dataIndex:"status",key:"status",render:function(e){return D(e)}},{title:"Progress",key:"progress",render:function(e,t){var n=g[t.task_id];return n?r.createElement("div",null,r.createElement($.A,{percent:n.progress,size:"small",status:"failed"===n.status?"exception":"active"}),r.createElement("div",{style:{fontSize:"12px",color:"#666"}},n.current_step)):r.createElement($.A,{percent:t.progress||0,size:"small"})}},{title:"Found/Scraped",key:"results",render:function(e,t){return r.createElement(K.A,{direction:"vertical",size:"small"},r.createElement("span",null,"Found: ",t.total_found||0),r.createElement("span",null,"Scraped: ",t.total_scraped||0))}},{title:"Created",dataIndex:"created_at",key:"created_at",render:function(e){return new Date(e).toLocaleString()}},{title:"Actions",key:"actions",render:function(e,t){return r.createElement(K.A,{size:"small"},"running"===t.status&&r.createElement(b.A,{title:"Stop Task"},r.createElement(A.Ay,{icon:r.createElement($e.A,null),size:"small",danger:!0,onClick:function(){return j(t.task_id)}})),r.createElement(b.A,{title:"View Results"},r.createElement(A.Ay,{icon:r.createElement(Je.A,null),size:"small",onClick:function(){return T(t.task_id)},disabled:!t.total_scraped})),"completed"===t.status&&t.total_scraped>0&&r.createElement(b.A,{title:"Export to Excel"},r.createElement(A.Ay,{icon:r.createElement(et.A,null),size:"small",type:"primary",onClick:function(){return F(t.task_id)}})))}}];return r.createElement("div",null,r.createElement(H.A,{justify:"space-between",align:"middle",style:{marginBottom:16}},r.createElement(V.A,null,r.createElement("h1",null,"Facebook Scraping")),r.createElement(V.A,null,r.createElement(A.Ay,{icon:r.createElement(ae.A,null),onClick:P,loading:n},"Refresh"))),r.createElement(H.A,{gutter:[16,16]},r.createElement(V.A,{xs:24,lg:12},r.createElement(X.A,{title:"Create Scraping Task"},r.createElement(ze.A,{form:e,layout:"vertical",onFinish:I},r.createElement(ze.A.Item,{name:"target_url",label:"Facebook Post URL",rules:[{required:!0,message:"Please enter Facebook post URL"},{type:"url",message:"Please enter a valid URL"}]},r.createElement(z.A,{placeholder:"https://www.facebook.com/..."})),r.createElement(ze.A.Item,{name:"profile_id",label:"Profile to Use",rules:[{required:!0,message:"Please select a profile"}]},r.createElement(je.A,{placeholder:"Select a logged-in profile"},s.map(function(e){return r.createElement(mt,{key:e.id,value:e.id},e.name," (",e.facebook_username,")")}))),r.createElement(ze.A.Item,{name:"scraping_types",label:"What to Scrape",initialValue:["all"]},r.createElement(Qe.A.Group,null,r.createElement(H.A,null,r.createElement(V.A,{span:24},r.createElement(Qe.A,{value:"all"},"All (Comments + Likes + Shares)")),r.createElement(V.A,{span:8},r.createElement(Qe.A,{value:"comments"},"Comments")),r.createElement(V.A,{span:8},r.createElement(Qe.A,{value:"likes"},"Likes")),r.createElement(V.A,{span:8},r.createElement(Qe.A,{value:"shares"},"Shares"))))),r.createElement(ze.A.Item,{name:"max_results",label:"Maximum Results",initialValue:1e3},r.createElement(Me.A,{min:1,max:1e4,style:{width:"100%"},placeholder:"Maximum number of users to scrape"})),r.createElement(ze.A.Item,null,r.createElement(A.Ay,{type:"primary",htmlType:"submit",icon:r.createElement(x.A,null),loading:n,block:!0},"Start Scraping"))))),r.createElement(V.A,{xs:24,lg:12},p&&g[p]&&r.createElement(X.A,{title:"Active Task Status"},r.createElement(K.A,{direction:"vertical",style:{width:"100%"}},r.createElement($.A,{percent:g[p].progress,status:"failed"===g[p].status?"exception":"active"}),r.createElement("div",null,r.createElement("strong",null,"Status:")," ",D(g[p].status)),r.createElement("div",null,r.createElement("strong",null,"Current Step:")," ",g[p].current_step),r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:12},r.createElement(Q.A,{title:"Total Found",value:g[p].total_found,prefix:r.createElement(h.A,null)})),r.createElement(V.A,{span:12},r.createElement(Q.A,{title:"Total Scraped",value:g[p].total_scraped,prefix:r.createElement(oe.A,null),valueStyle:{color:"#3f8600"}}))),r.createElement(A.Ay,{danger:!0,icon:r.createElement($e.A,null),onClick:function(){return j(p)},block:!0},"Stop Task"))))),r.createElement(X.A,{title:"Scraping Tasks",style:{marginTop:24}},r.createElement(De.A,{columns:R,dataSource:i,rowKey:"id",loading:n,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e){return"Total ".concat(e," tasks")}}})),r.createElement(Re.A,{title:"Scraping Results",open:v,onCancel:function(){return C(!1)},width:1e3,footer:[r.createElement(A.Ay,{key:"close",onClick:function(){return C(!1)}},"Close")]},w&&r.createElement("div",null,r.createElement(H.A,{gutter:16,style:{marginBottom:16}},r.createElement(V.A,{span:6},r.createElement(Q.A,{title:"Total Users",value:w.total_users})),r.createElement(V.A,{span:6},r.createElement(Q.A,{title:"Comments",value:w.users_by_type.comment||0})),r.createElement(V.A,{span:6},r.createElement(Q.A,{title:"Likes",value:w.users_by_type.like||0})),r.createElement(V.A,{span:6},r.createElement(Q.A,{title:"Shares",value:w.users_by_type.share||0}))),r.createElement(De.A,{size:"small",columns:[{title:"UID",dataIndex:"facebook_uid",key:"facebook_uid",width:120},{title:"Name",dataIndex:"full_name",key:"full_name"},{title:"Type",dataIndex:"interaction_type",key:"interaction_type",render:function(e){return r.createElement(te.A,null,e)}},{title:"Content",dataIndex:"interaction_content",key:"interaction_content",render:function(e){return e&&e.length>50?e.substring(0,50)+"...":e||"-"}},{title:"Scraped At",dataIndex:"scraped_at",key:"scraped_at",render:function(e){return new Date(e).toLocaleString()}}],dataSource:w.users,rowKey:"id",pagination:{pageSize:10}}))))};var At=n(5308),ft=n(9552),dt=n(7028);function gt(e){return gt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},gt(e)}function yt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function bt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?yt(Object(n),!0).forEach(function(t){Et(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):yt(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Et(e,t,n){return(t=function(e){var t=function(e){if("object"!=gt(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=gt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==gt(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ht(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function o(n,r,a,o){var c=r&&r.prototype instanceof l?r:l,s=Object.create(c.prototype);return xt(s,"_invoke",function(n,r,a){var o,l,c,s=0,u=a||[],m=!1,p={p:0,n:0,v:e,a:A,f:A.bind(e,4),d:function(t,n){return o=t,l=0,c=e,p.n=n,i}};function A(n,r){for(l=n,c=r,t=0;!m&&s&&!a&&t<u.length;t++){var a,o=u[t],A=p.p,f=o[2];n>3?(a=f===r)&&(c=o[(l=o[4])?5:(l=3,3)],o[4]=o[5]=e):o[0]<=A&&((a=n<2&&A<o[1])?(l=0,p.v=r,p.n=o[1]):A<f&&(a=n<3||o[0]>r||r>f)&&(o[4]=n,o[5]=r,p.n=f,l=0))}if(a||n>1)return i;throw m=!0,r}return function(a,u,f){if(s>1)throw TypeError("Generator is already running");for(m&&1===u&&A(u,f),l=u,c=f;(t=l<2?e:c)||!m;){o||(l?l<3?(l>1&&(p.n=-1),A(l,c)):p.n=c:p.v=c);try{if(s=2,o){if(l||(a="next"),t=o[a]){if(!(t=t.call(o,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,l<2&&(l=0)}else 1===l&&(t=o.return)&&t.call(o),l<2&&(c=TypeError("The iterator does not provide a '"+a+"' method"),l=1);o=e}else if((t=(m=p.n<0)?c:n.call(r,p))!==i)break}catch(t){o=e,l=1,c=t}finally{s=1}}return{value:t,done:m}}}(n,a,o),!0),s}var i={};function l(){}function c(){}function s(){}t=Object.getPrototypeOf;var u=[][r]?t(t([][r]())):(xt(t={},r,function(){return this}),t),m=s.prototype=l.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,xt(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e}return c.prototype=s,xt(m,"constructor",s),xt(s,"constructor",c),c.displayName="GeneratorFunction",xt(s,a,"GeneratorFunction"),xt(m),xt(m,a,"Generator"),xt(m,r,function(){return this}),xt(m,"toString",function(){return"[object Generator]"}),(ht=function(){return{w:o,m:p}})()}function xt(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}xt=function(e,t,n,r){if(t)a?a(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var o=function(t,n){xt(e,t,function(e){return this._invoke(t,n,e)})};o("next",0),o("throw",1),o("return",2)}},xt(e,t,n,r)}function vt(e,t,n,r,a,o,i){try{var l=e[o](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,a)}function Ct(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var o=e.apply(t,n);function i(e){vt(o,r,a,i,l,"next",e)}function l(e){vt(o,r,a,i,l,"throw",e)}i(void 0)})}}function kt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return wt(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?wt(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var Bt=je.A.Option,St=z.A.TextArea;const _t=function(){var e=kt(ze.A.useForm(),1)[0],t=kt((0,r.useState)(!1),2),n=t[0],a=t[1],o=kt((0,r.useState)([]),2),i=o[0],l=o[1],c=kt((0,r.useState)([]),2),s=c[0],u=c[1],m=kt((0,r.useState)(null),2),p=m[0],f=m[1],d=kt((0,r.useState)(null),2),g=d[0],y=d[1],E=kt((0,r.useState)({}),2),x=E[0],C=E[1],k=kt((0,r.useState)({}),2),w=(k[0],k[1]),B=kt((0,r.useState)(!1),2),S=B[0],_=B[1],P=kt((0,r.useState)(null),2),I=P[0],O=P[1];(0,r.useEffect)(function(){j();var e=setInterval(function(){g&&D(g)},2e3);return function(){return clearInterval(e)}},[g]);var j=function(){var e=Ct(ht().m(function e(){var t,n,r,o,i;return ht().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,a(!0),e.n=1,Promise.all([xe.getProfiles(),xe.get("/api/messaging/")]);case 1:t=e.v,n=kt(t,2),r=n[0],o=n[1],u(r.filter(function(e){return e.facebook_logged_in})),l(o),e.n=3;break;case 2:e.p=2,i=e.v,Te.Ay.error("Failed to load data: "+i.message);case 3:return e.p=3,a(!1),e.f(3);case 4:return e.a(2)}},e,null,[[0,2,3,4]])}));return function(){return e.apply(this,arguments)}}(),T=function(){var e=Ct(ht().m(function e(t){var n,r,a;return ht().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,(n=new FormData).append("file",t),e.n=1,xe.post("/api/messaging/upload-recipients",n,{headers:{"Content-Type":"multipart/form-data"}});case 1:return r=e.v,f(r),Te.Ay.success("Uploaded ".concat(r.total_recipients," recipients successfully")),e.a(2,!1);case 2:return e.p=2,a=e.v,Te.Ay.error("Failed to upload file: "+a.message),e.a(2,!1)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}(),F=function(){var t=Ct(ht().m(function t(n){var r,o,i;return ht().w(function(t){for(;;)switch(t.n){case 0:return t.p=0,a(!0),r={name:n.name,sender_profile_ids:n.sender_profile_ids,recipient_list_file:null==p?void 0:p.file_path,message_template:n.message_template,message_type:n.message_type||"text",image_paths:n.image_paths,concurrent_threads:n.concurrent_threads||1,messages_per_account_min:n.messages_per_account_min||1,messages_per_account_max:n.messages_per_account_max||10,delay_between_messages_min:n.delay_between_messages_min||5,delay_between_messages_max:n.delay_between_messages_max||15,avoid_duplicate_uids:!1!==n.avoid_duplicate_uids,randomize_message:!0===n.randomize_message},t.n=1,xe.post("/api/messaging/start",{config:r});case 1:o=t.v,y(o.task_id),Te.Ay.success("Messaging task started successfully"),e.resetFields(),f(null),j(),t.n=3;break;case 2:t.p=2,i=t.v,Te.Ay.error("Failed to start messaging: "+i.message);case 3:return t.p=3,a(!1),t.f(3);case 4:return t.a(2)}},t,null,[[0,2,3,4]])}));return function(e){return t.apply(this,arguments)}}(),D=function(){var e=Ct(ht().m(function e(t){var n,r,a,o,i;return ht().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,Promise.all([xe.get("/api/messaging/status/".concat(t)),xe.get("/api/messaging/worker-stats/".concat(t)).catch(function(){return null})]);case 1:n=e.v,r=kt(n,2),a=r[0],o=r[1],C(function(e){return bt(bt({},e),{},Et({},t,a))}),o&&w(function(e){return bt(bt({},e),{},Et({},t,o))}),["completed","failed","cancelled"].includes(a.status)&&(y(null),j()),e.n=3;break;case 2:e.p=2,i=e.v,console.error("Failed to poll task status:",i);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}(),R=function(){var e=Ct(ht().m(function e(t){var n;return ht().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,xe.post("/api/messaging/stop/".concat(t));case 1:Te.Ay.success("Task stopped successfully"),y(null),j(),e.n=3;break;case 2:e.p=2,n=e.v,Te.Ay.error("Failed to stop task: "+n.message);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}(),M=function(){var e=Ct(ht().m(function e(t){var n,r;return ht().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,xe.get("/api/messaging/results/".concat(t));case 1:n=e.v,O(n),_(!0),e.n=3;break;case 2:e.p=2,r=e.v,Te.Ay.error("Failed to load results: "+r.message);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}(),U=function(e){var t={pending:{color:"default",text:"Pending"},running:{color:"processing",text:"Running"},completed:{color:"success",text:"Completed"},failed:{color:"error",text:"Failed"},cancelled:{color:"default",text:"Cancelled"}},n=t[e]||t.pending;return r.createElement(te.A,{color:n.color},n.text)},N=[{title:"Task Name",dataIndex:"name",key:"name"},{title:"Status",dataIndex:"status",key:"status",render:function(e){return U(e)}},{title:"Progress",key:"progress",render:function(e,t){var n=x[t.task_id];return n?r.createElement("div",null,r.createElement($.A,{percent:n.progress,size:"small",status:"failed"===n.status?"exception":"active"}),r.createElement("div",{style:{fontSize:"12px",color:"#666"}},n.current_step)):r.createElement($.A,{percent:0,size:"small"})}},{title:"Recipients",dataIndex:"total_recipients",key:"total_recipients"},{title:"Sent/Failed/Skipped",key:"stats",render:function(e,t){return r.createElement(K.A,{direction:"vertical",size:"small"},r.createElement("span",{style:{color:"#52c41a"}},"✓ ",t.messages_sent||0),r.createElement("span",{style:{color:"#ff4d4f"}},"✗ ",t.messages_failed||0),r.createElement("span",{style:{color:"#faad14"}},"⏸ ",t.messages_skipped||0))}},{title:"Created",dataIndex:"created_at",key:"created_at",render:function(e){return new Date(e).toLocaleString()}},{title:"Actions",key:"actions",render:function(e,t){return r.createElement(K.A,{size:"small"},"running"===t.status&&r.createElement(b.A,{title:"Stop Task"},r.createElement(A.Ay,{icon:r.createElement($e.A,null),size:"small",danger:!0,onClick:function(){return R(t.task_id)}})),r.createElement(b.A,{title:"View Results"},r.createElement(A.Ay,{icon:r.createElement(Je.A,null),size:"small",onClick:function(){return M(t.task_id)}})))}}];return r.createElement("div",null,r.createElement(H.A,{justify:"space-between",align:"middle",style:{marginBottom:16}},r.createElement(V.A,null,r.createElement("h1",null,"Bulk Messaging")),r.createElement(V.A,null,r.createElement(A.Ay,{icon:r.createElement(ae.A,null),onClick:j,loading:n},"Refresh"))),r.createElement(H.A,{gutter:[16,16]},r.createElement(V.A,{xs:24,lg:12},r.createElement(X.A,{title:"Create Messaging Task"},r.createElement(ze.A,{form:e,layout:"vertical",onFinish:F},r.createElement(ze.A.Item,{name:"name",label:"Task Name",rules:[{required:!0,message:"Please enter task name"}]},r.createElement(z.A,{placeholder:"Enter task name"})),r.createElement(ze.A.Item,{name:"sender_profile_ids",label:"Sender Profiles",rules:[{required:!0,message:"Please select sender profiles"}]},r.createElement(je.A,{mode:"multiple",placeholder:"Select profiles to send messages from",optionFilterProp:"children"},s.map(function(e){return r.createElement(Bt,{key:e.id,value:e.id},e.name," (",e.facebook_username,")")}))),r.createElement(ze.A.Item,{label:"Recipient List"},r.createElement(At.A,{beforeUpload:T,accept:".csv,.xlsx,.xls",showUploadList:!1},r.createElement(A.Ay,{icon:r.createElement(dt.A,null)},"Upload Recipients (CSV/Excel)")),p&&r.createElement(Z.A,{message:"".concat(p.total_recipients," recipients loaded from ").concat(p.list_name),type:"success",style:{marginTop:8},showIcon:!0})),r.createElement(ze.A.Item,{name:"message_template",label:"Message Template",rules:[{required:!0,message:"Please enter message template"}]},r.createElement(St,{rows:4,placeholder:"Enter your message template. Use {name} for recipient name."})),r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"concurrent_threads",label:"Concurrent Threads",initialValue:1},r.createElement(Me.A,{min:1,max:10,style:{width:"100%"}}))),r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"message_type",label:"Message Type",initialValue:"text"},r.createElement(je.A,null,r.createElement(Bt,{value:"text"},"Text Only"),r.createElement(Bt,{value:"image"},"Image Only"),r.createElement(Bt,{value:"text_with_image"},"Text + Image"))))),r.createElement(A.Ay,{type:"primary",htmlType:"submit",icon:r.createElement(v.A,null),loading:n,disabled:!p,block:!0},"Start Messaging")))),r.createElement(V.A,{xs:24,lg:12},g&&x[g]&&r.createElement(X.A,{title:"Active Task Status"},r.createElement(K.A,{direction:"vertical",style:{width:"100%"}},r.createElement($.A,{percent:x[g].progress,status:"failed"===x[g].status?"exception":"active"}),r.createElement("div",null,r.createElement("strong",null,"Status:")," ",U(x[g].status)),r.createElement("div",null,r.createElement("strong",null,"Current Step:")," ",x[g].current_step),r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:8},r.createElement(Q.A,{title:"Total Recipients",value:x[g].total_recipients,prefix:r.createElement(h.A,null)})),r.createElement(V.A,{span:8},r.createElement(Q.A,{title:"Messages Sent",value:x[g].messages_sent,prefix:r.createElement(oe.A,null),valueStyle:{color:"#3f8600"}})),r.createElement(V.A,{span:8},r.createElement(Q.A,{title:"Failed",value:x[g].messages_failed,prefix:r.createElement(ft.A,null),valueStyle:{color:"#cf1322"}}))),r.createElement(A.Ay,{danger:!0,icon:r.createElement($e.A,null),onClick:function(){return R(g)},block:!0},"Stop Task"))))),r.createElement(X.A,{title:"Messaging Tasks",style:{marginTop:24}},r.createElement(De.A,{columns:N,dataSource:i,rowKey:"id",loading:n,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e){return"Total ".concat(e," tasks")}}})),r.createElement(Re.A,{title:"Messaging Results",open:S,onCancel:function(){return _(!1)},width:800,footer:[r.createElement(A.Ay,{key:"close",onClick:function(){return _(!1)}},"Close")]},I&&r.createElement("div",null,r.createElement(H.A,{gutter:16,style:{marginBottom:16}},r.createElement(V.A,{span:6},r.createElement(Q.A,{title:"Total Recipients",value:I.total_recipients})),r.createElement(V.A,{span:6},r.createElement(Q.A,{title:"Messages Sent",value:I.messages_sent})),r.createElement(V.A,{span:6},r.createElement(Q.A,{title:"Failed",value:I.messages_failed})),r.createElement(V.A,{span:6},r.createElement(Q.A,{title:"Success Rate",value:I.success_rate,suffix:"%"}))),r.createElement(De.A,{size:"small",columns:[{title:"Status",dataIndex:"status",key:"status",render:function(e){return r.createElement(K.A,null,function(e){switch(e){case"sent":return r.createElement(oe.A,{style:{color:"#52c41a"}});case"failed":return r.createElement(ft.A,{style:{color:"#ff4d4f"}});case"skipped":return r.createElement(re.A,{style:{color:"#faad14"}});default:return r.createElement(re.A,{style:{color:"#d9d9d9"}})}}(e),e)}},{title:"Recipient",dataIndex:"recipient_name",key:"recipient_name",render:function(e,t){return e||t.recipient_uid}},{title:"Message",dataIndex:"message_content",key:"message_content",render:function(e){return e.length>50?e.substring(0,50)+"...":e}},{title:"Sent At",dataIndex:"sent_at",key:"sent_at",render:function(e){return e?new Date(e).toLocaleString():"-"}}],dataSource:I.messages,rowKey:"id",pagination:{pageSize:10}}))))};var Pt=n(2609),It=n(1575),Ot=n(3903);function jt(e){return jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jt(e)}function zt(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function o(n,r,a,o){var c=r&&r.prototype instanceof l?r:l,s=Object.create(c.prototype);return Tt(s,"_invoke",function(n,r,a){var o,l,c,s=0,u=a||[],m=!1,p={p:0,n:0,v:e,a:A,f:A.bind(e,4),d:function(t,n){return o=t,l=0,c=e,p.n=n,i}};function A(n,r){for(l=n,c=r,t=0;!m&&s&&!a&&t<u.length;t++){var a,o=u[t],A=p.p,f=o[2];n>3?(a=f===r)&&(c=o[(l=o[4])?5:(l=3,3)],o[4]=o[5]=e):o[0]<=A&&((a=n<2&&A<o[1])?(l=0,p.v=r,p.n=o[1]):A<f&&(a=n<3||o[0]>r||r>f)&&(o[4]=n,o[5]=r,p.n=f,l=0))}if(a||n>1)return i;throw m=!0,r}return function(a,u,f){if(s>1)throw TypeError("Generator is already running");for(m&&1===u&&A(u,f),l=u,c=f;(t=l<2?e:c)||!m;){o||(l?l<3?(l>1&&(p.n=-1),A(l,c)):p.n=c:p.v=c);try{if(s=2,o){if(l||(a="next"),t=o[a]){if(!(t=t.call(o,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,l<2&&(l=0)}else 1===l&&(t=o.return)&&t.call(o),l<2&&(c=TypeError("The iterator does not provide a '"+a+"' method"),l=1);o=e}else if((t=(m=p.n<0)?c:n.call(r,p))!==i)break}catch(t){o=e,l=1,c=t}finally{s=1}}return{value:t,done:m}}}(n,a,o),!0),s}var i={};function l(){}function c(){}function s(){}t=Object.getPrototypeOf;var u=[][r]?t(t([][r]())):(Tt(t={},r,function(){return this}),t),m=s.prototype=l.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,Tt(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e}return c.prototype=s,Tt(m,"constructor",s),Tt(s,"constructor",c),c.displayName="GeneratorFunction",Tt(s,a,"GeneratorFunction"),Tt(m),Tt(m,a,"Generator"),Tt(m,r,function(){return this}),Tt(m,"toString",function(){return"[object Generator]"}),(zt=function(){return{w:o,m:p}})()}function Tt(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}Tt=function(e,t,n,r){if(t)a?a(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var o=function(t,n){Tt(e,t,function(e){return this._invoke(t,n,e)})};o("next",0),o("throw",1),o("return",2)}},Tt(e,t,n,r)}function Ft(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Dt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ft(Object(n),!0).forEach(function(t){Rt(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ft(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Rt(e,t,n){return(t=function(e){var t=function(e){if("object"!=jt(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=jt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==jt(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Mt(e,t,n,r,a,o,i){try{var l=e[o](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,a)}function Ut(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var o=e.apply(t,n);function i(e){Mt(o,r,a,i,l,"next",e)}function l(e){Mt(o,r,a,i,l,"throw",e)}i(void 0)})}}function Nt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Lt(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Lt(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Lt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var qt=je.A.Option;const Wt=function(){var e=Nt(ze.A.useForm(),1)[0],t=Nt((0,r.useState)(!1),2),n=t[0],a=t[1],o=Nt((0,r.useState)({maxConcurrentBrowsers:5,browserTimeout:3e4,headlessMode:!1,maxScrapingWorkers:3,scrapingDelayMin:2,scrapingDelayMax:5,autoExportResults:!0,maxMessagingWorkers:5,messageDelayMin:5,messageDelayMax:15,avoidDuplicateUIDs:!0,randomizeMessages:!1,requestsPerMinute:30,requestsPerHour:500,enableLogging:!0,logLevel:"info",autoCleanupLogs:!0,maxLogFileSize:100,theme:"light",language:"en",autoRefreshInterval:30}),2),i=o[0],l=o[1];(0,r.useEffect)(function(){c()},[]);var c=function(){var t=Ut(zt().m(function t(){var n,r;return zt().w(function(t){for(;;)switch(t.n){case 0:return t.p=0,t.n=1,window.electronAPI.store.get("app_settings");case 1:(n=t.v)?(l(Dt(Dt({},i),n)),e.setFieldsValue(Dt(Dt({},i),n))):e.setFieldsValue(i),t.n=3;break;case 2:t.p=2,r=t.v,console.error("Failed to load settings:",r),e.setFieldsValue(i);case 3:return t.a(2)}},t,null,[[0,2]])}));return function(){return t.apply(this,arguments)}}(),s=function(){var e=Ut(zt().m(function e(t){var n;return zt().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,a(!0),e.n=1,window.electronAPI.store.set("app_settings",t);case 1:l(t),Te.Ay.success("Settings saved successfully"),e.n=3;break;case 2:e.p=2,n=e.v,console.error("Failed to save settings:",n),Te.Ay.error("Failed to save settings");case 3:return e.p=3,a(!1),e.f(3);case 4:return e.a(2)}},e,null,[[0,2,3,4]])}));return function(t){return e.apply(this,arguments)}}();return r.createElement("div",null,r.createElement("h1",{style:{marginBottom:24}},"Settings"),r.createElement(ze.A,{form:e,layout:"vertical",onFinish:s,initialValues:i},r.createElement(X.A,{title:"Browser Settings",style:{marginBottom:16}},r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"maxConcurrentBrowsers",label:"Max Concurrent Browsers",tooltip:"Maximum number of browser instances that can run simultaneously"},r.createElement(Me.A,{min:1,max:10,style:{width:"100%"}}))),r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"browserTimeout",label:"Browser Timeout (ms)",tooltip:"Timeout for browser operations in milliseconds"},r.createElement(Me.A,{min:5e3,max:12e4,step:1e3,style:{width:"100%"}})))),r.createElement(ze.A.Item,{name:"headlessMode",label:"Headless Mode",valuePropName:"checked",tooltip:"Run browsers in headless mode (not recommended for Facebook automation)"},r.createElement(Pt.A,null))),r.createElement(X.A,{title:"Scraping Settings",style:{marginBottom:16}},r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:8},r.createElement(ze.A.Item,{name:"maxScrapingWorkers",label:"Max Scraping Workers",tooltip:"Number of concurrent scraping workers"},r.createElement(Me.A,{min:1,max:10,style:{width:"100%"}}))),r.createElement(V.A,{span:8},r.createElement(ze.A.Item,{name:"scrapingDelayMin",label:"Min Delay (seconds)",tooltip:"Minimum delay between scraping actions"},r.createElement(Me.A,{min:1,max:30,style:{width:"100%"}}))),r.createElement(V.A,{span:8},r.createElement(ze.A.Item,{name:"scrapingDelayMax",label:"Max Delay (seconds)",tooltip:"Maximum delay between scraping actions"},r.createElement(Me.A,{min:1,max:60,style:{width:"100%"}})))),r.createElement(ze.A.Item,{name:"autoExportResults",label:"Auto Export Results",valuePropName:"checked",tooltip:"Automatically export scraping results to Excel"},r.createElement(Pt.A,null))),r.createElement(X.A,{title:"Messaging Settings",style:{marginBottom:16}},r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:8},r.createElement(ze.A.Item,{name:"maxMessagingWorkers",label:"Max Messaging Workers",tooltip:"Number of concurrent messaging workers"},r.createElement(Me.A,{min:1,max:10,style:{width:"100%"}}))),r.createElement(V.A,{span:8},r.createElement(ze.A.Item,{name:"messageDelayMin",label:"Min Delay (seconds)",tooltip:"Minimum delay between messages"},r.createElement(Me.A,{min:1,max:60,style:{width:"100%"}}))),r.createElement(V.A,{span:8},r.createElement(ze.A.Item,{name:"messageDelayMax",label:"Max Delay (seconds)",tooltip:"Maximum delay between messages"},r.createElement(Me.A,{min:1,max:120,style:{width:"100%"}})))),r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"avoidDuplicateUIDs",label:"Avoid Duplicate UIDs",valuePropName:"checked",tooltip:"Prevent sending messages to the same UID from multiple accounts"},r.createElement(Pt.A,null))),r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"randomizeMessages",label:"Randomize Messages",valuePropName:"checked",tooltip:"Add random variations to message templates"},r.createElement(Pt.A,null))))),r.createElement(X.A,{title:"Rate Limiting",style:{marginBottom:16}},r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"requestsPerMinute",label:"Requests Per Minute",tooltip:"Maximum requests per minute per profile"},r.createElement(It.A,{min:10,max:100,marks:{10:"10",30:"30",60:"60",100:"100"}}))),r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"requestsPerHour",label:"Requests Per Hour",tooltip:"Maximum requests per hour per profile"},r.createElement(It.A,{min:100,max:2e3,marks:{100:"100",500:"500",1e3:"1K",2e3:"2K"}}))))),r.createElement(X.A,{title:"System Settings",style:{marginBottom:16}},r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"enableLogging",label:"Enable Logging",valuePropName:"checked"},r.createElement(Pt.A,null))),r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"logLevel",label:"Log Level"},r.createElement(je.A,null,r.createElement(qt,{value:"debug"},"Debug"),r.createElement(qt,{value:"info"},"Info"),r.createElement(qt,{value:"warning"},"Warning"),r.createElement(qt,{value:"error"},"Error"))))),r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"autoCleanupLogs",label:"Auto Cleanup Logs",valuePropName:"checked",tooltip:"Automatically delete old log files"},r.createElement(Pt.A,null))),r.createElement(V.A,{span:12},r.createElement(ze.A.Item,{name:"maxLogFileSize",label:"Max Log File Size (MB)"},r.createElement(Me.A,{min:10,max:1e3,style:{width:"100%"}}))))),r.createElement(X.A,{title:"UI Settings",style:{marginBottom:16}},r.createElement(H.A,{gutter:16},r.createElement(V.A,{span:8},r.createElement(ze.A.Item,{name:"theme",label:"Theme"},r.createElement(je.A,null,r.createElement(qt,{value:"light"},"Light"),r.createElement(qt,{value:"dark"},"Dark")))),r.createElement(V.A,{span:8},r.createElement(ze.A.Item,{name:"language",label:"Language"},r.createElement(je.A,null,r.createElement(qt,{value:"en"},"English"),r.createElement(qt,{value:"vi"},"Tiếng Việt")))),r.createElement(V.A,{span:8},r.createElement(ze.A.Item,{name:"autoRefreshInterval",label:"Auto Refresh (seconds)",tooltip:"Auto refresh interval for dashboard and status"},r.createElement(Me.A,{min:10,max:300,style:{width:"100%"}}))))),r.createElement(X.A,null,r.createElement(K.A,null,r.createElement(A.Ay,{type:"primary",icon:r.createElement(Ot.A,null),htmlType:"submit",loading:n},"Save Settings"),r.createElement(A.Ay,{icon:r.createElement(ae.A,null),onClick:function(){e.setFieldsValue(i),Te.Ay.info("Settings reset to last saved values")}},"Reset"),r.createElement(A.Ay,{onClick:function(){e.setFieldsValue({maxConcurrentBrowsers:5,browserTimeout:3e4,headlessMode:!1,maxScrapingWorkers:3,scrapingDelayMin:2,scrapingDelayMax:5,autoExportResults:!0,maxMessagingWorkers:5,messageDelayMin:5,messageDelayMax:15,avoidDuplicateUIDs:!0,randomizeMessages:!1,requestsPerMinute:30,requestsPerHour:500,enableLogging:!0,logLevel:"info",autoCleanupLogs:!0,maxLogFileSize:100,theme:"light",language:"en",autoRefreshInterval:30}),Te.Ay.info("Settings reset to default values")}},"Restore Defaults")))))};function Gt(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function o(n,r,a,o){var c=r&&r.prototype instanceof l?r:l,s=Object.create(c.prototype);return Yt(s,"_invoke",function(n,r,a){var o,l,c,s=0,u=a||[],m=!1,p={p:0,n:0,v:e,a:A,f:A.bind(e,4),d:function(t,n){return o=t,l=0,c=e,p.n=n,i}};function A(n,r){for(l=n,c=r,t=0;!m&&s&&!a&&t<u.length;t++){var a,o=u[t],A=p.p,f=o[2];n>3?(a=f===r)&&(c=o[(l=o[4])?5:(l=3,3)],o[4]=o[5]=e):o[0]<=A&&((a=n<2&&A<o[1])?(l=0,p.v=r,p.n=o[1]):A<f&&(a=n<3||o[0]>r||r>f)&&(o[4]=n,o[5]=r,p.n=f,l=0))}if(a||n>1)return i;throw m=!0,r}return function(a,u,f){if(s>1)throw TypeError("Generator is already running");for(m&&1===u&&A(u,f),l=u,c=f;(t=l<2?e:c)||!m;){o||(l?l<3?(l>1&&(p.n=-1),A(l,c)):p.n=c:p.v=c);try{if(s=2,o){if(l||(a="next"),t=o[a]){if(!(t=t.call(o,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,l<2&&(l=0)}else 1===l&&(t=o.return)&&t.call(o),l<2&&(c=TypeError("The iterator does not provide a '"+a+"' method"),l=1);o=e}else if((t=(m=p.n<0)?c:n.call(r,p))!==i)break}catch(t){o=e,l=1,c=t}finally{s=1}}return{value:t,done:m}}}(n,a,o),!0),s}var i={};function l(){}function c(){}function s(){}t=Object.getPrototypeOf;var u=[][r]?t(t([][r]())):(Yt(t={},r,function(){return this}),t),m=s.prototype=l.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,Yt(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e}return c.prototype=s,Yt(m,"constructor",s),Yt(s,"constructor",c),c.displayName="GeneratorFunction",Yt(s,a,"GeneratorFunction"),Yt(m),Yt(m,a,"Generator"),Yt(m,r,function(){return this}),Yt(m,"toString",function(){return"[object Generator]"}),(Gt=function(){return{w:o,m:p}})()}function Yt(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}Yt=function(e,t,n,r){if(t)a?a(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var o=function(t,n){Yt(e,t,function(e){return this._invoke(t,n,e)})};o("next",0),o("throw",1),o("return",2)}},Yt(e,t,n,r)}function Ht(e,t,n,r,a,o,i){try{var l=e[o](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,a)}function Vt(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var o=e.apply(t,n);function i(e){Ht(o,r,a,i,l,"next",e)}function l(e){Ht(o,r,a,i,l,"throw",e)}i(void 0)})}}function Kt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Zt(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Zt(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Zt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var Xt=i.A.Content;const Qt=function(){var e=Kt((0,r.useState)(!1),2),t=e[0],n=e[1],a=Kt((0,r.useState)(!0),2),o=a[0],A=a[1],f=Kt((0,r.useState)("connecting"),2),d=(f[0],f[1]);(0,r.useEffect)(function(){g()},[]);var g=function(){var e=Vt(Gt().m(function e(){var t;return Gt().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,A(!0),e.n=1,y();case 1:return e.n=2,b();case 2:l.Ay.success({message:"Welcome to Facebook Automation Desktop",description:"All systems are ready. You can start automating your Facebook tasks.",duration:4,placement:"topRight"}),e.n=4;break;case 3:e.p=3,t=e.v,console.error("App initialization failed:",t),l.Ay.error({message:"Initialization Failed",description:"Failed to initialize the application. Please check your backend connection.",duration:6,placement:"topRight"});case 4:return e.p=4,A(!1),e.f(4);case 5:return e.a(2)}},e,null,[[0,3,4,5]])}));return function(){return e.apply(this,arguments)}}(),y=function(){var e=Vt(Gt().m(function e(){var t;return Gt().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,xe.get("/health");case 1:"healthy"===e.v.status?d("connected"):d("error"),e.n=3;break;case 2:throw e.p=2,t=e.v,console.error("Backend connection failed:",t),d("error"),t;case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}(),b=function(){var e=Vt(Gt().m(function e(){var t;return Gt().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,Promise.all([xe.getProfiles().catch(function(){return[]}),xe.get("/api/system/stats").catch(function(){return{}})]);case 1:e.n=3;break;case 2:e.p=2,t=e.v,console.error("Failed to load initial data:",t);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}();return o?r.createElement("div",{className:"app-loading"},r.createElement("div",{className:"loading-content"},r.createElement("div",{className:"loading-logo"},"📱"),r.createElement("h2",null,"Facebook Automation Desktop"),r.createElement(c.A,{size:"large"}),r.createElement("p",null,"Initializing application..."),r.createElement("div",{className:"loading-progress"},r.createElement("div",{className:"progress-bar"}))),r.createElement("style",{jsx:!0},"\n          .app-loading {\n            position: fixed;\n            top: 0;\n            left: 0;\n            width: 100vw;\n            height: 100vh;\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            z-index: 9999;\n          }\n\n          .loading-content {\n            text-align: center;\n            color: white;\n          }\n\n          .loading-logo {\n            font-size: 80px;\n            margin-bottom: 20px;\n            animation: pulse 2s infinite;\n          }\n\n          .loading-content h2 {\n            color: white;\n            margin-bottom: 30px;\n            font-size: 28px;\n            font-weight: 300;\n          }\n\n          .loading-content p {\n            color: rgba(255, 255, 255, 0.8);\n            margin: 20px 0;\n            font-size: 16px;\n          }\n\n          .loading-progress {\n            width: 200px;\n            height: 4px;\n            background: rgba(255, 255, 255, 0.2);\n            border-radius: 2px;\n            margin: 20px auto;\n            overflow: hidden;\n          }\n\n          .progress-bar {\n            width: 100%;\n            height: 100%;\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);\n            animation: loading 2s infinite;\n          }\n\n          @keyframes pulse {\n            0% { transform: scale(1); }\n            50% { transform: scale(1.1); }\n            100% { transform: scale(1); }\n          }\n\n          @keyframes loading {\n            0% { transform: translateX(-100%); }\n            100% { transform: translateX(100%); }\n          }\n        ")):r.createElement(u.Kd,null,r.createElement(i.A,{style:{minHeight:"100vh",background:"#f5f5f5"}},r.createElement(j,{collapsed:t,setCollapsed:n}),r.createElement(i.A,{style:{marginLeft:t?80:280,transition:"all 0.3s ease",minHeight:"100vh"}},r.createElement(G,{collapsed:t,setCollapsed:n}),r.createElement(Xt,{style:{marginTop:80,padding:"24px",minHeight:"calc(100vh - 80px)",background:"#f5f5f5",overflow:"auto"}},r.createElement("div",{className:"content-wrapper",style:{maxWidth:"1400px",margin:"0 auto",animation:"fadeIn 0.5s ease-in"}},r.createElement(m.BV,null,r.createElement(m.qh,{path:"/",element:r.createElement(Oe,null)}),r.createElement(m.qh,{path:"/profiles",element:r.createElement(Xe,null)}),r.createElement(m.qh,{path:"/scraping",element:r.createElement(pt,null)}),r.createElement(m.qh,{path:"/messaging",element:r.createElement(_t,null)}),r.createElement(m.qh,{path:"/settings",element:r.createElement(Wt,null)}),r.createElement(m.qh,{path:"*",element:r.createElement(m.C5,{to:"/",replace:!0})}))))),r.createElement(s.A,{style:{right:"30px",bottom:"30px"}},r.createElement("div",{style:{height:"50px",width:"50px",lineHeight:"50px",borderRadius:"25px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",textAlign:"center",fontSize:"18px",boxShadow:"0 4px 12px rgba(102, 126, 234, 0.4)",transition:"all 0.3s ease"}},r.createElement(p.A,null))),r.createElement("style",{jsx:!0,global:!0},"\n          /* Fade in animation */\n          @keyframes fadeIn {\n            from { opacity: 0; transform: translateY(20px); }\n            to { opacity: 1; transform: translateY(0); }\n          }\n\n          /* Custom scrollbar */\n          ::-webkit-scrollbar {\n            width: 8px;\n            height: 8px;\n          }\n\n          ::-webkit-scrollbar-track {\n            background: #f1f1f1;\n            border-radius: 4px;\n          }\n\n          ::-webkit-scrollbar-thumb {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            border-radius: 4px;\n          }\n\n          ::-webkit-scrollbar-thumb:hover {\n            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n          }\n\n          /* Card hover effects */\n          .custom-card {\n            border-radius: 16px !important;\n            box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;\n            border: none !important;\n            overflow: hidden !important;\n            transition: all 0.3s ease !important;\n          }\n\n          .custom-card:hover {\n            box-shadow: 0 8px 30px rgba(0,0,0,0.12) !important;\n            transform: translateY(-2px) !important;\n          }\n\n          .custom-card .ant-card-head {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\n            border-bottom: none !important;\n            border-radius: 16px 16px 0 0 !important;\n          }\n\n          .custom-card .ant-card-head-title {\n            color: white !important;\n            font-weight: 600 !important;\n          }\n\n          /* Button styles */\n          .gradient-button {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\n            border: none !important;\n            border-radius: 12px !important;\n            color: white !important;\n            font-weight: 500 !important;\n            transition: all 0.3s ease !important;\n          }\n\n          .gradient-button:hover {\n            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;\n            transform: translateY(-1px) !important;\n            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;\n          }\n\n          /* Progress bars */\n          .custom-progress .ant-progress-bg {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\n          }\n\n          /* Tables */\n          .custom-table .ant-table-thead > tr > th {\n            background: #fafafa !important;\n            border-bottom: 2px solid #f0f0f0 !important;\n            font-weight: 600 !important;\n            color: #262626 !important;\n          }\n\n          .custom-table .ant-table-tbody > tr:hover > td {\n            background: #f5f5f5 !important;\n          }\n\n          /* Forms */\n          .custom-form .ant-form-item-label > label {\n            font-weight: 500 !important;\n            color: #262626 !important;\n          }\n\n          .custom-form .ant-input,\n          .custom-form .ant-select-selector {\n            border-radius: 8px !important;\n            border: 1px solid #d9d9d9 !important;\n            transition: all 0.3s ease !important;\n          }\n\n          .custom-form .ant-input:focus,\n          .custom-form .ant-select-focused .ant-select-selector {\n            border-color: #667eea !important;\n            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;\n          }\n\n          /* Notifications */\n          .ant-notification {\n            border-radius: 12px !important;\n            box-shadow: 0 8px 30px rgba(0,0,0,0.12) !important;\n          }\n\n          /* Animation classes */\n          .fade-in {\n            animation: fadeIn 0.5s ease-in;\n          }\n\n          .slide-up {\n            animation: slideUp 0.5s ease-out;\n          }\n\n          @keyframes slideUp {\n            from { opacity: 0; transform: translateY(30px); }\n            to { opacity: 1; transform: translateY(0); }\n          }\n\n          .bounce-in {\n            animation: bounceIn 0.6s ease-out;\n          }\n\n          @keyframes bounceIn {\n            0% { opacity: 0; transform: scale(0.3); }\n            50% { opacity: 1; transform: scale(1.05); }\n            70% { transform: scale(0.9); }\n            100% { opacity: 1; transform: scale(1); }\n          }\n\n          /* Responsive design */\n          @media (max-width: 768px) {\n            .content-wrapper {\n              padding: 0 8px !important;\n            }\n            \n            .custom-card {\n              margin-bottom: 16px !important;\n            }\n          }\n        ")))};var $t=n(5072),Jt=n.n($t),en=n(7825),tn=n.n(en),nn=n(7659),rn=n.n(nn),an=n(5056),on=n.n(an),ln=n(540),cn=n.n(ln),sn=n(1113),un=n.n(sn),mn=n(6962),pn={};pn.styleTagTransform=un(),pn.setAttributes=on(),pn.insert=rn().bind(null,"head"),pn.domAPI=tn(),pn.insertStyleElement=cn(),Jt()(mn.A,pn),mn.A&&mn.A.locals&&mn.A.locals;var An={token:{colorPrimary:"#667eea",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff4d4f",colorInfo:"#1890ff",borderRadius:8,fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},components:{Button:{borderRadius:8,controlHeight:40},Input:{borderRadius:8,controlHeight:40},Card:{borderRadius:16},Menu:{borderRadius:8}}};const fn=function(){return r.createElement(o.Ay,{theme:An},r.createElement("div",{className:"App"},r.createElement(Qt,null)))};n(6940);var dn=n(5171),gn={};gn.styleTagTransform=un(),gn.setAttributes=on(),gn.insert=rn().bind(null,"head"),gn.domAPI=tn(),gn.insertStyleElement=cn(),Jt()(dn.A,gn),dn.A&&dn.A.locals&&dn.A.locals;var yn=document.getElementById("root");(0,a.H)(yn).render(r.createElement(r.StrictMode,null,r.createElement(fn,null)))},5171:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(1354),a=n.n(r),o=n(6314),i=n.n(o)()(a());i.push([e.id,"/**\n * Global styles for Facebook Automation Desktop\n */\n\n/* Reset and base styles */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\n/* Scrollbar styles */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* Custom utility classes */\n.text-center {\n  text-align: center;\n}\n\n.text-left {\n  text-align: left;\n}\n\n.text-right {\n  text-align: right;\n}\n\n.mb-16 {\n  margin-bottom: 16px;\n}\n\n.mb-24 {\n  margin-bottom: 24px;\n}\n\n.mt-16 {\n  margin-top: 16px;\n}\n\n.mt-24 {\n  margin-top: 24px;\n}\n\n.p-16 {\n  padding: 16px;\n}\n\n.p-24 {\n  padding: 24px;\n}\n\n/* Status indicators */\n.status-online {\n  color: #52c41a;\n}\n\n.status-offline {\n  color: #ff4d4f;\n}\n\n.status-warning {\n  color: #faad14;\n}\n\n/* Card styles */\n.custom-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: box-shadow 0.3s ease;\n}\n\n.custom-card:hover {\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\n\n/* Form styles */\n.form-section {\n  margin-bottom: 24px;\n  padding: 16px;\n  background: #fafafa;\n  border-radius: 6px;\n  border: 1px solid #d9d9d9;\n}\n\n.form-section-title {\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 16px;\n  color: #262626;\n}\n\n/* Table styles */\n.custom-table .ant-table-thead > tr > th {\n  background-color: #fafafa;\n  font-weight: 600;\n}\n\n.custom-table .ant-table-tbody > tr:hover > td {\n  background-color: #f5f5f5;\n}\n\n/* Button styles */\n.btn-success {\n  background-color: #52c41a;\n  border-color: #52c41a;\n}\n\n.btn-success:hover {\n  background-color: #73d13d;\n  border-color: #73d13d;\n}\n\n.btn-danger {\n  background-color: #ff4d4f;\n  border-color: #ff4d4f;\n}\n\n.btn-danger:hover {\n  background-color: #ff7875;\n  border-color: #ff7875;\n}\n\n/* Progress styles */\n.progress-container {\n  padding: 16px;\n  background: #f9f9f9;\n  border-radius: 6px;\n  margin: 16px 0;\n}\n\n.progress-stats {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n  font-size: 14px;\n  color: #666;\n}\n\n/* Loading styles */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n/* Responsive styles */\n@media (max-width: 768px) {\n  .ant-layout-sider {\n    position: fixed !important;\n    height: 100vh;\n    z-index: 999;\n  }\n  \n  .ant-layout-content {\n    margin-left: 0 !important;\n  }\n}\n","",{version:3,sources:["webpack://./src/styles/global.css"],names:[],mappings:"AAAA;;EAEE;;AAEF,0BAA0B;AAC1B;EACE,sBAAsB;AACxB;;AAEA;EACE,SAAS;EACT,UAAU;EACV;;cAEY;EACZ,mCAAmC;EACnC,kCAAkC;EAClC,yBAAyB;AAC3B;;AAEA,qBAAqB;AACrB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;AACrB;;AAEA,2BAA2B;AAC3B;EACE,kBAAkB;AACpB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,iBAAiB;AACnB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,aAAa;AACf;;AAEA,sBAAsB;AACtB;EACE,cAAc;AAChB;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,cAAc;AAChB;;AAEA,gBAAgB;AAChB;EACE,kBAAkB;EAClB,wCAAwC;EACxC,gCAAgC;AAClC;;AAEA;EACE,0CAA0C;AAC5C;;AAEA,gBAAgB;AAChB;EACE,mBAAmB;EACnB,aAAa;EACb,mBAAmB;EACnB,kBAAkB;EAClB,yBAAyB;AAC3B;;AAEA;EACE,eAAe;EACf,gBAAgB;EAChB,mBAAmB;EACnB,cAAc;AAChB;;AAEA,iBAAiB;AACjB;EACE,yBAAyB;EACzB,gBAAgB;AAClB;;AAEA;EACE,yBAAyB;AAC3B;;AAEA,kBAAkB;AAClB;EACE,yBAAyB;EACzB,qBAAqB;AACvB;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;AACvB;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;AACvB;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;AACvB;;AAEA,oBAAoB;AACpB;EACE,aAAa;EACb,mBAAmB;EACnB,kBAAkB;EAClB,cAAc;AAChB;;AAEA;EACE,aAAa;EACb,8BAA8B;EAC9B,kBAAkB;EAClB,eAAe;EACf,WAAW;AACb;;AAEA,mBAAmB;AACnB;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,oCAAoC;EACpC,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,aAAa;AACf;;AAEA,sBAAsB;AACtB;EACE;IACE,0BAA0B;IAC1B,aAAa;IACb,YAAY;EACd;;EAEA;IACE,yBAAyB;EAC3B;AACF",sourcesContent:["/**\n * Global styles for Facebook Automation Desktop\n */\n\n/* Reset and base styles */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\n/* Scrollbar styles */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* Custom utility classes */\n.text-center {\n  text-align: center;\n}\n\n.text-left {\n  text-align: left;\n}\n\n.text-right {\n  text-align: right;\n}\n\n.mb-16 {\n  margin-bottom: 16px;\n}\n\n.mb-24 {\n  margin-bottom: 24px;\n}\n\n.mt-16 {\n  margin-top: 16px;\n}\n\n.mt-24 {\n  margin-top: 24px;\n}\n\n.p-16 {\n  padding: 16px;\n}\n\n.p-24 {\n  padding: 24px;\n}\n\n/* Status indicators */\n.status-online {\n  color: #52c41a;\n}\n\n.status-offline {\n  color: #ff4d4f;\n}\n\n.status-warning {\n  color: #faad14;\n}\n\n/* Card styles */\n.custom-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: box-shadow 0.3s ease;\n}\n\n.custom-card:hover {\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\n\n/* Form styles */\n.form-section {\n  margin-bottom: 24px;\n  padding: 16px;\n  background: #fafafa;\n  border-radius: 6px;\n  border: 1px solid #d9d9d9;\n}\n\n.form-section-title {\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 16px;\n  color: #262626;\n}\n\n/* Table styles */\n.custom-table .ant-table-thead > tr > th {\n  background-color: #fafafa;\n  font-weight: 600;\n}\n\n.custom-table .ant-table-tbody > tr:hover > td {\n  background-color: #f5f5f5;\n}\n\n/* Button styles */\n.btn-success {\n  background-color: #52c41a;\n  border-color: #52c41a;\n}\n\n.btn-success:hover {\n  background-color: #73d13d;\n  border-color: #73d13d;\n}\n\n.btn-danger {\n  background-color: #ff4d4f;\n  border-color: #ff4d4f;\n}\n\n.btn-danger:hover {\n  background-color: #ff7875;\n  border-color: #ff7875;\n}\n\n/* Progress styles */\n.progress-container {\n  padding: 16px;\n  background: #f9f9f9;\n  border-radius: 6px;\n  margin: 16px 0;\n}\n\n.progress-stats {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n  font-size: 14px;\n  color: #666;\n}\n\n/* Loading styles */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n/* Responsive styles */\n@media (max-width: 768px) {\n  .ant-layout-sider {\n    position: fixed !important;\n    height: 100vh;\n    z-index: 999;\n  }\n  \n  .ant-layout-content {\n    margin-left: 0 !important;\n  }\n}\n"],sourceRoot:""}]);const l=i},6962:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(1354),a=n.n(r),o=n(6314),i=n.n(o)()(a());i.push([e.id,"/* Facebook Automation Desktop - Main App Styles */\n\n/* Global Styles */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background: #f5f5f5;\n}\n\n/* App Loading Screen */\n.app-loading {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.loading-content {\n  text-align: center;\n  color: white;\n}\n\n.loading-logo {\n  font-size: 80px;\n  margin-bottom: 20px;\n  animation: pulse 2s infinite;\n}\n\n.loading-content h2 {\n  color: white;\n  margin-bottom: 30px;\n  font-size: 28px;\n  font-weight: 300;\n}\n\n.loading-content p {\n  color: rgba(255, 255, 255, 0.8);\n  margin-top: 20px;\n  font-size: 16px;\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.1); }\n  100% { transform: scale(1); }\n}\n\n/* Sidebar Logo */\n.app-logo {\n  height: 80px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.1);\n  margin: 16px;\n  border-radius: 8px;\n  backdrop-filter: blur(10px);\n}\n\n.logo-container {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.logo-icon {\n  font-size: 32px;\n  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));\n}\n\n.logo-icon-collapsed {\n  font-size: 28px;\n  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));\n}\n\n.logo-text {\n  color: white;\n  line-height: 1.2;\n}\n\n.logo-title {\n  font-size: 16px;\n  font-weight: bold;\n  margin: 0;\n}\n\n.logo-subtitle {\n  font-size: 12px;\n  opacity: 0.8;\n  margin: 0;\n}\n\n/* Custom Menu Styles */\n.custom-menu .ant-menu-item {\n  margin: 4px 8px !important;\n  border-radius: 8px !important;\n  height: 48px !important;\n  line-height: 48px !important;\n  transition: all 0.3s ease !important;\n}\n\n.custom-menu .ant-menu-item:hover {\n  background: rgba(255, 255, 255, 0.15) !important;\n  transform: translateX(4px);\n}\n\n.custom-menu .ant-menu-item-selected {\n  background: rgba(255, 255, 255, 0.2) !important;\n  border-radius: 8px !important;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;\n}\n\n.custom-menu .ant-menu-item-selected::after {\n  display: none !important;\n}\n\n.custom-menu .ant-menu-item .ant-menu-item-icon {\n  font-size: 18px;\n}\n\n/* Header Styles */\n.app-header {\n  border-bottom: 1px solid #f0f0f0;\n  backdrop-filter: blur(10px);\n  background: rgba(255, 255, 255, 0.95) !important;\n}\n\n.trigger {\n  color: #666;\n  border-radius: 4px;\n}\n\n.trigger:hover {\n  color: #1890ff;\n  background: #f0f0f0;\n}\n\n/* Content Wrapper */\n.content-wrapper {\n  max-width: 1400px;\n  margin: 0 auto;\n  animation: fadeIn 0.5s ease-in;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n/* Card Styles */\n.custom-card {\n  border-radius: 12px;\n  box-shadow: 0 4px 20px rgba(0,0,0,0.08);\n  border: none;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.custom-card:hover {\n  box-shadow: 0 8px 30px rgba(0,0,0,0.12);\n  transform: translateY(-2px);\n}\n\n.custom-card .ant-card-head {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-bottom: none;\n}\n\n.custom-card .ant-card-head-title {\n  color: white;\n  font-weight: 600;\n}\n\n/* Status Indicators */\n.status-indicator {\n  display: inline-flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.status-online {\n  background: #f6ffed;\n  color: #52c41a;\n  border: 1px solid #b7eb8f;\n}\n\n.status-offline {\n  background: #fff2f0;\n  color: #ff4d4f;\n  border: 1px solid #ffccc7;\n}\n\n.status-connecting {\n  background: #e6f7ff;\n  color: #1890ff;\n  border: 1px solid #91d5ff;\n}\n\n/* Progress Bars */\n.custom-progress .ant-progress-bg {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n/* Buttons */\n.gradient-button {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 8px;\n  color: white;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.gradient-button:hover {\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n}\n\n/* Tables */\n.custom-table .ant-table-thead > tr > th {\n  background: #fafafa;\n  border-bottom: 2px solid #f0f0f0;\n  font-weight: 600;\n  color: #262626;\n}\n\n.custom-table .ant-table-tbody > tr:hover > td {\n  background: #f5f5f5;\n}\n\n/* Forms */\n.custom-form .ant-form-item-label > label {\n  font-weight: 500;\n  color: #262626;\n}\n\n.custom-form .ant-input,\n.custom-form .ant-select-selector {\n  border-radius: 8px;\n  border: 1px solid #d9d9d9;\n  transition: all 0.3s ease;\n}\n\n.custom-form .ant-input:focus,\n.custom-form .ant-select-focused .ant-select-selector {\n  border-color: #667eea;\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\n}\n\n/* Notifications */\n.ant-notification {\n  border-radius: 12px;\n  box-shadow: 0 8px 30px rgba(0,0,0,0.12);\n}\n\n/* Scrollbar Styles */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .content-wrapper {\n    padding: 0 8px;\n  }\n  \n  .custom-card {\n    margin-bottom: 16px;\n  }\n  \n  .app-header h1 {\n    font-size: 16px;\n  }\n}\n\n/* Dark Mode Support */\n@media (prefers-color-scheme: dark) {\n  body {\n    background: #141414;\n    color: #fff;\n  }\n  \n  .custom-card {\n    background: #1f1f1f;\n    border: 1px solid #303030;\n  }\n  \n  .custom-table .ant-table-thead > tr > th {\n    background: #262626;\n    color: #fff;\n  }\n}\n\n/* Animation Classes */\n.fade-in {\n  animation: fadeIn 0.5s ease-in;\n}\n\n.slide-up {\n  animation: slideUp 0.5s ease-out;\n}\n\n@keyframes slideUp {\n  from { opacity: 0; transform: translateY(30px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n.bounce-in {\n  animation: bounceIn 0.6s ease-out;\n}\n\n@keyframes bounceIn {\n  0% { opacity: 0; transform: scale(0.3); }\n  50% { opacity: 1; transform: scale(1.05); }\n  70% { transform: scale(0.9); }\n  100% { opacity: 1; transform: scale(1); }\n}\n","",{version:3,sources:["webpack://./src/styles/App.css"],names:[],mappings:"AAAA,kDAAkD;;AAElD,kBAAkB;AAClB;EACE,sBAAsB;AACxB;;AAEA;EACE,SAAS;EACT,UAAU;EACV;;cAEY;EACZ,mCAAmC;EACnC,kCAAkC;EAClC,mBAAmB;AACrB;;AAEA,uBAAuB;AACvB;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,YAAY;EACZ,aAAa;EACb,6DAA6D;EAC7D,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,aAAa;AACf;;AAEA;EACE,kBAAkB;EAClB,YAAY;AACd;;AAEA;EACE,eAAe;EACf,mBAAmB;EACnB,4BAA4B;AAC9B;;AAEA;EACE,YAAY;EACZ,mBAAmB;EACnB,eAAe;EACf,gBAAgB;AAClB;;AAEA;EACE,+BAA+B;EAC/B,gBAAgB;EAChB,eAAe;AACjB;;AAEA;EACE,KAAK,mBAAmB,EAAE;EAC1B,MAAM,qBAAqB,EAAE;EAC7B,OAAO,mBAAmB,EAAE;AAC9B;;AAEA,iBAAiB;AACjB;EACE,YAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,oCAAoC;EACpC,YAAY;EACZ,kBAAkB;EAClB,2BAA2B;AAC7B;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,SAAS;AACX;;AAEA;EACE,eAAe;EACf,8CAA8C;AAChD;;AAEA;EACE,eAAe;EACf,8CAA8C;AAChD;;AAEA;EACE,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,eAAe;EACf,iBAAiB;EACjB,SAAS;AACX;;AAEA;EACE,eAAe;EACf,YAAY;EACZ,SAAS;AACX;;AAEA,uBAAuB;AACvB;EACE,0BAA0B;EAC1B,6BAA6B;EAC7B,uBAAuB;EACvB,4BAA4B;EAC5B,oCAAoC;AACtC;;AAEA;EACE,gDAAgD;EAChD,0BAA0B;AAC5B;;AAEA;EACE,+CAA+C;EAC/C,6BAA6B;EAC7B,kDAAkD;AACpD;;AAEA;EACE,wBAAwB;AAC1B;;AAEA;EACE,eAAe;AACjB;;AAEA,kBAAkB;AAClB;EACE,gCAAgC;EAChC,2BAA2B;EAC3B,gDAAgD;AAClD;;AAEA;EACE,WAAW;EACX,kBAAkB;AACpB;;AAEA;EACE,cAAc;EACd,mBAAmB;AACrB;;AAEA,oBAAoB;AACpB;EACE,iBAAiB;EACjB,cAAc;EACd,8BAA8B;AAChC;;AAEA;EACE,OAAO,UAAU,EAAE,2BAA2B,EAAE;EAChD,KAAK,UAAU,EAAE,wBAAwB,EAAE;AAC7C;;AAEA,gBAAgB;AAChB;EACE,mBAAmB;EACnB,uCAAuC;EACvC,YAAY;EACZ,gBAAgB;EAChB,yBAAyB;AAC3B;;AAEA;EACE,uCAAuC;EACvC,2BAA2B;AAC7B;;AAEA;EACE,6DAA6D;EAC7D,mBAAmB;AACrB;;AAEA;EACE,YAAY;EACZ,gBAAgB;AAClB;;AAEA,sBAAsB;AACtB;EACE,oBAAoB;EACpB,mBAAmB;EACnB,QAAQ;EACR,iBAAiB;EACjB,mBAAmB;EACnB,eAAe;EACf,gBAAgB;AAClB;;AAEA;EACE,mBAAmB;EACnB,cAAc;EACd,yBAAyB;AAC3B;;AAEA;EACE,mBAAmB;EACnB,cAAc;EACd,yBAAyB;AAC3B;;AAEA;EACE,mBAAmB;EACnB,cAAc;EACd,yBAAyB;AAC3B;;AAEA,kBAAkB;AAClB;EACE,6DAA6D;AAC/D;;AAEA,YAAY;AACZ;EACE,6DAA6D;EAC7D,YAAY;EACZ,kBAAkB;EAClB,YAAY;EACZ,gBAAgB;EAChB,yBAAyB;AAC3B;;AAEA;EACE,6DAA6D;EAC7D,2BAA2B;EAC3B,+CAA+C;AACjD;;AAEA,WAAW;AACX;EACE,mBAAmB;EACnB,gCAAgC;EAChC,gBAAgB;EAChB,cAAc;AAChB;;AAEA;EACE,mBAAmB;AACrB;;AAEA,UAAU;AACV;EACE,gBAAgB;EAChB,cAAc;AAChB;;AAEA;;EAEE,kBAAkB;EAClB,yBAAyB;EACzB,yBAAyB;AAC3B;;AAEA;;EAEE,qBAAqB;EACrB,8CAA8C;AAChD;;AAEA,kBAAkB;AAClB;EACE,mBAAmB;EACnB,uCAAuC;AACzC;;AAEA,qBAAqB;AACrB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,6DAA6D;EAC7D,kBAAkB;AACpB;;AAEA;EACE,6DAA6D;AAC/D;;AAEA,sBAAsB;AACtB;EACE;IACE,cAAc;EAChB;;EAEA;IACE,mBAAmB;EACrB;;EAEA;IACE,eAAe;EACjB;AACF;;AAEA,sBAAsB;AACtB;EACE;IACE,mBAAmB;IACnB,WAAW;EACb;;EAEA;IACE,mBAAmB;IACnB,yBAAyB;EAC3B;;EAEA;IACE,mBAAmB;IACnB,WAAW;EACb;AACF;;AAEA,sBAAsB;AACtB;EACE,8BAA8B;AAChC;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,OAAO,UAAU,EAAE,2BAA2B,EAAE;EAChD,KAAK,UAAU,EAAE,wBAAwB,EAAE;AAC7C;;AAEA;EACE,iCAAiC;AACnC;;AAEA;EACE,KAAK,UAAU,EAAE,qBAAqB,EAAE;EACxC,MAAM,UAAU,EAAE,sBAAsB,EAAE;EAC1C,MAAM,qBAAqB,EAAE;EAC7B,OAAO,UAAU,EAAE,mBAAmB,EAAE;AAC1C",sourcesContent:["/* Facebook Automation Desktop - Main App Styles */\n\n/* Global Styles */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background: #f5f5f5;\n}\n\n/* App Loading Screen */\n.app-loading {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.loading-content {\n  text-align: center;\n  color: white;\n}\n\n.loading-logo {\n  font-size: 80px;\n  margin-bottom: 20px;\n  animation: pulse 2s infinite;\n}\n\n.loading-content h2 {\n  color: white;\n  margin-bottom: 30px;\n  font-size: 28px;\n  font-weight: 300;\n}\n\n.loading-content p {\n  color: rgba(255, 255, 255, 0.8);\n  margin-top: 20px;\n  font-size: 16px;\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.1); }\n  100% { transform: scale(1); }\n}\n\n/* Sidebar Logo */\n.app-logo {\n  height: 80px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.1);\n  margin: 16px;\n  border-radius: 8px;\n  backdrop-filter: blur(10px);\n}\n\n.logo-container {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.logo-icon {\n  font-size: 32px;\n  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));\n}\n\n.logo-icon-collapsed {\n  font-size: 28px;\n  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));\n}\n\n.logo-text {\n  color: white;\n  line-height: 1.2;\n}\n\n.logo-title {\n  font-size: 16px;\n  font-weight: bold;\n  margin: 0;\n}\n\n.logo-subtitle {\n  font-size: 12px;\n  opacity: 0.8;\n  margin: 0;\n}\n\n/* Custom Menu Styles */\n.custom-menu .ant-menu-item {\n  margin: 4px 8px !important;\n  border-radius: 8px !important;\n  height: 48px !important;\n  line-height: 48px !important;\n  transition: all 0.3s ease !important;\n}\n\n.custom-menu .ant-menu-item:hover {\n  background: rgba(255, 255, 255, 0.15) !important;\n  transform: translateX(4px);\n}\n\n.custom-menu .ant-menu-item-selected {\n  background: rgba(255, 255, 255, 0.2) !important;\n  border-radius: 8px !important;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;\n}\n\n.custom-menu .ant-menu-item-selected::after {\n  display: none !important;\n}\n\n.custom-menu .ant-menu-item .ant-menu-item-icon {\n  font-size: 18px;\n}\n\n/* Header Styles */\n.app-header {\n  border-bottom: 1px solid #f0f0f0;\n  backdrop-filter: blur(10px);\n  background: rgba(255, 255, 255, 0.95) !important;\n}\n\n.trigger {\n  color: #666;\n  border-radius: 4px;\n}\n\n.trigger:hover {\n  color: #1890ff;\n  background: #f0f0f0;\n}\n\n/* Content Wrapper */\n.content-wrapper {\n  max-width: 1400px;\n  margin: 0 auto;\n  animation: fadeIn 0.5s ease-in;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n/* Card Styles */\n.custom-card {\n  border-radius: 12px;\n  box-shadow: 0 4px 20px rgba(0,0,0,0.08);\n  border: none;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.custom-card:hover {\n  box-shadow: 0 8px 30px rgba(0,0,0,0.12);\n  transform: translateY(-2px);\n}\n\n.custom-card .ant-card-head {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-bottom: none;\n}\n\n.custom-card .ant-card-head-title {\n  color: white;\n  font-weight: 600;\n}\n\n/* Status Indicators */\n.status-indicator {\n  display: inline-flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.status-online {\n  background: #f6ffed;\n  color: #52c41a;\n  border: 1px solid #b7eb8f;\n}\n\n.status-offline {\n  background: #fff2f0;\n  color: #ff4d4f;\n  border: 1px solid #ffccc7;\n}\n\n.status-connecting {\n  background: #e6f7ff;\n  color: #1890ff;\n  border: 1px solid #91d5ff;\n}\n\n/* Progress Bars */\n.custom-progress .ant-progress-bg {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n/* Buttons */\n.gradient-button {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 8px;\n  color: white;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.gradient-button:hover {\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n}\n\n/* Tables */\n.custom-table .ant-table-thead > tr > th {\n  background: #fafafa;\n  border-bottom: 2px solid #f0f0f0;\n  font-weight: 600;\n  color: #262626;\n}\n\n.custom-table .ant-table-tbody > tr:hover > td {\n  background: #f5f5f5;\n}\n\n/* Forms */\n.custom-form .ant-form-item-label > label {\n  font-weight: 500;\n  color: #262626;\n}\n\n.custom-form .ant-input,\n.custom-form .ant-select-selector {\n  border-radius: 8px;\n  border: 1px solid #d9d9d9;\n  transition: all 0.3s ease;\n}\n\n.custom-form .ant-input:focus,\n.custom-form .ant-select-focused .ant-select-selector {\n  border-color: #667eea;\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\n}\n\n/* Notifications */\n.ant-notification {\n  border-radius: 12px;\n  box-shadow: 0 8px 30px rgba(0,0,0,0.12);\n}\n\n/* Scrollbar Styles */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .content-wrapper {\n    padding: 0 8px;\n  }\n  \n  .custom-card {\n    margin-bottom: 16px;\n  }\n  \n  .app-header h1 {\n    font-size: 16px;\n  }\n}\n\n/* Dark Mode Support */\n@media (prefers-color-scheme: dark) {\n  body {\n    background: #141414;\n    color: #fff;\n  }\n  \n  .custom-card {\n    background: #1f1f1f;\n    border: 1px solid #303030;\n  }\n  \n  .custom-table .ant-table-thead > tr > th {\n    background: #262626;\n    color: #fff;\n  }\n}\n\n/* Animation Classes */\n.fade-in {\n  animation: fadeIn 0.5s ease-in;\n}\n\n.slide-up {\n  animation: slideUp 0.5s ease-out;\n}\n\n@keyframes slideUp {\n  from { opacity: 0; transform: translateY(30px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n.bounce-in {\n  animation: bounceIn 0.6s ease-out;\n}\n\n@keyframes bounceIn {\n  0% { opacity: 0; transform: scale(0.3); }\n  50% { opacity: 1; transform: scale(1.05); }\n  70% { transform: scale(0.9); }\n  100% { opacity: 1; transform: scale(1); }\n}\n"],sourceRoot:""}]);const l=i}},a={};function o(e){var t=a[e];if(void 0!==t)return t.exports;var n=a[e]={id:e,exports:{}};return r[e](n,n.exports,o),n.exports}o.m=r,e=[],o.O=(t,n,r,a)=>{if(!n){var i=1/0;for(u=0;u<e.length;u++){for(var[n,r,a]=e[u],l=!0,c=0;c<n.length;c++)(!1&a||i>=a)&&Object.keys(o.O).every(e=>o.O[e](n[c]))?n.splice(c--,1):(l=!1,a<i&&(i=a));if(l){e.splice(u--,1);var s=r();void 0!==s&&(t=s)}}return t}a=a||0;for(var u=e.length;u>0&&e[u-1][2]>a;u--)e[u]=e[u-1];e[u]=[n,r,a]},o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,o.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var a=Object.create(null);o.r(a);var i={};t=t||[null,n({}),n([]),n(n)];for(var l=2&r&&e;("object"==typeof l||"function"==typeof l)&&!~t.indexOf(l);l=n(l))Object.getOwnPropertyNames(l).forEach(t=>i[t]=()=>e[t]);return i.default=()=>e,o.d(a,i),a},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e={792:0};o.O.j=t=>0===e[t];var t=(t,n)=>{var r,a,[i,l,c]=n,s=0;if(i.some(t=>0!==e[t])){for(r in l)o.o(l,r)&&(o.m[r]=l[r]);if(c)var u=c(o)}for(t&&t(n);s<i.length;s++)a=i[s],o.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return o.O(u)},n=global.webpackChunkfacebook_automation_desktop=global.webpackChunkfacebook_automation_desktop||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),o.nc=void 0;var i=o.O(void 0,[96],()=>o(1498));i=o.O(i)})();
//# sourceMappingURL=main.bedb7068bf18577fa311.js.map