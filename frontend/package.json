{"name": "facebook-automation-desktop", "version": "1.0.0", "description": "Desktop application for Facebook automation with antidetect browser profiles", "main": "../src/main/main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd ../backend && python -m uvicorn main:app --reload --port 8000", "dev:frontend": "electron . --dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "npm run webpack:build", "build:backend": "cd ../backend && python -m PyInstaller --onefile main.py", "webpack:build": "webpack --mode production", "webpack:dev": "webpack --mode development --watch", "test": "jest", "lint": "eslint ../src/ renderer/", "package": "electron-builder", "dist": "npm run build && electron-builder"}, "keywords": ["facebook", "automation", "scraping", "antidetect", "browser", "desktop"], "author": "Your Name", "license": "MIT", "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "babel-loader": "^9.1.0", "concurrently": "^8.2.0", "css-loader": "^6.8.0", "electron": "^25.0.0", "electron-builder": "^24.0.0", "eslint": "^8.42.0", "html-webpack-plugin": "^5.5.0", "jest": "^29.5.0", "style-loader": "^3.3.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0"}, "dependencies": {"@ant-design/icons": "^5.1.0", "antd": "^5.6.0", "axios": "^1.4.0", "electron-store": "^8.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.13.0", "socket.io-client": "^4.7.0"}, "build": {"appId": "com.yourcompany.facebook-automation", "productName": "Facebook Automation Desktop", "directories": {"output": "dist"}, "files": ["../src/main/**/*", "dist/**/*", "../backend/dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}