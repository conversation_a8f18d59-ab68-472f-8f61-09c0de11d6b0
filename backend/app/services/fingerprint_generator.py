"""
Browser Fingerprint Generator for Antidetect Profiles
"""

import random
import json
from typing import Dict, Any, List
from faker import Faker

fake = Faker()


class FingerprintGenerator:
    """Generate realistic browser fingerprints for antidetect profiles"""
    
    def __init__(self):
        self.user_agents = [
            # Chrome on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            
            # Chrome on macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            
            # Firefox on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0",
            
            # Firefox on macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0",
            
            # Edge on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
        ]
        
        self.screen_resolutions = [
            {"width": 1920, "height": 1080},
            {"width": 1366, "height": 768},
            {"width": 1440, "height": 900},
            {"width": 1536, "height": 864},
            {"width": 1600, "height": 900},
            {"width": 1280, "height": 720},
            {"width": 1280, "height": 800},
            {"width": 1024, "height": 768},
        ]
        
        self.languages = [
            "en-US,en;q=0.9",
            "en-GB,en;q=0.9",
            "vi-VN,vi;q=0.9,en;q=0.8",
            "zh-CN,zh;q=0.9,en;q=0.8",
            "ja-JP,ja;q=0.9,en;q=0.8",
            "ko-KR,ko;q=0.9,en;q=0.8",
            "es-ES,es;q=0.9,en;q=0.8",
            "fr-FR,fr;q=0.9,en;q=0.8",
            "de-DE,de;q=0.9,en;q=0.8",
        ]
        
        self.timezones = [
            "Asia/Ho_Chi_Minh",
            "America/New_York",
            "America/Los_Angeles",
            "Europe/London",
            "Europe/Paris",
            "Asia/Tokyo",
            "Asia/Seoul",
            "Asia/Shanghai",
            "Australia/Sydney",
        ]
        
        self.platforms = [
            {"name": "Win32", "os": "Windows"},
            {"name": "MacIntel", "os": "macOS"},
            {"name": "Linux x86_64", "os": "Linux"},
        ]
    
    async def generate_fingerprint(self) -> Dict[str, Any]:
        """Generate a complete browser fingerprint"""
        
        # Select random components
        user_agent = random.choice(self.user_agents)
        screen_resolution = random.choice(self.screen_resolutions)
        language = random.choice(self.languages)
        timezone = random.choice(self.timezones)
        platform = random.choice(self.platforms)
        
        # Generate viewport (slightly smaller than screen)
        viewport_width = screen_resolution["width"] - random.randint(0, 100)
        viewport_height = screen_resolution["height"] - random.randint(100, 200)
        
        # Generate WebGL and Canvas fingerprints
        webgl_vendor = self._generate_webgl_vendor(user_agent)
        webgl_renderer = self._generate_webgl_renderer(user_agent)
        canvas_fingerprint = self._generate_canvas_fingerprint()
        
        # Generate hardware concurrency
        hardware_concurrency = random.choice([2, 4, 6, 8, 12, 16])
        
        # Generate memory (deviceMemory)
        device_memory = random.choice([2, 4, 8, 16, 32])
        
        fingerprint = {
            "user_agent": user_agent,
            "viewport": {
                "width": viewport_width,
                "height": viewport_height
            },
            "screen": {
                "width": screen_resolution["width"],
                "height": screen_resolution["height"],
                "color_depth": random.choice([24, 32]),
                "pixel_depth": random.choice([24, 32])
            },
            "language": language,
            "languages": language.split(",")[0].split(";")[0].split(","),
            "timezone": timezone,
            "platform": platform["name"],
            "os": platform["os"],
            "hardware_concurrency": hardware_concurrency,
            "device_memory": device_memory,
            "webgl": {
                "vendor": webgl_vendor,
                "renderer": webgl_renderer
            },
            "canvas_fingerprint": canvas_fingerprint,
            "audio_fingerprint": self._generate_audio_fingerprint(),
            "fonts": self._generate_font_list(platform["os"]),
            "plugins": self._generate_plugin_list(user_agent),
            "do_not_track": random.choice([None, "1"]),
            "cookie_enabled": True,
            "online": True,
            "java_enabled": False,
            "pdf_viewer_enabled": random.choice([True, False])
        }
        
        return fingerprint
    
    def _generate_webgl_vendor(self, user_agent: str) -> str:
        """Generate WebGL vendor based on user agent"""
        if "Chrome" in user_agent or "Edge" in user_agent:
            return "Google Inc. (Intel)"
        elif "Firefox" in user_agent:
            return "Mozilla"
        else:
            return "WebKit"
    
    def _generate_webgl_renderer(self, user_agent: str) -> str:
        """Generate WebGL renderer based on user agent"""
        renderers = [
            "ANGLE (Intel, Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "ANGLE (NVIDIA, NVIDIA GeForce GTX 1050 Ti Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "ANGLE (NVIDIA, NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "Intel(R) Iris(TM) Plus Graphics 640",
            "AMD Radeon Pro 560X OpenGL Engine",
            "Mesa DRI Intel(R) UHD Graphics 620 (KBL GT2)"
        ]
        return random.choice(renderers)
    
    def _generate_canvas_fingerprint(self) -> str:
        """Generate a unique canvas fingerprint"""
        # Simulate canvas fingerprint hash
        return fake.sha256()[:16]
    
    def _generate_audio_fingerprint(self) -> str:
        """Generate audio context fingerprint"""
        return fake.sha256()[:16]
    
    def _generate_font_list(self, os: str) -> List[str]:
        """Generate font list based on OS"""
        common_fonts = [
            "Arial", "Arial Black", "Arial Narrow", "Calibri", "Cambria",
            "Comic Sans MS", "Courier New", "Georgia", "Impact", "Lucida Console",
            "Lucida Sans Unicode", "Microsoft Sans Serif", "Palatino Linotype",
            "Segoe UI", "Tahoma", "Times New Roman", "Trebuchet MS", "Verdana"
        ]
        
        if os == "macOS":
            mac_fonts = [
                "Apple Chancery", "Apple Color Emoji", "Apple SD Gothic Neo",
                "Avenir", "Avenir Next", "Helvetica", "Helvetica Neue",
                "Menlo", "Monaco", "San Francisco", "SF Pro Display"
            ]
            common_fonts.extend(mac_fonts)
        elif os == "Linux":
            linux_fonts = [
                "DejaVu Sans", "DejaVu Sans Mono", "DejaVu Serif",
                "Liberation Sans", "Liberation Serif", "Liberation Mono",
                "Ubuntu", "Ubuntu Mono"
            ]
            common_fonts.extend(linux_fonts)
        
        # Return random subset of fonts
        min_fonts = min(20, len(common_fonts))
        max_fonts = len(common_fonts)
        num_fonts = random.randint(min_fonts, max_fonts)
        return random.sample(common_fonts, num_fonts)
    
    def _generate_plugin_list(self, user_agent: str) -> List[Dict[str, str]]:
        """Generate plugin list based on browser"""
        plugins = []
        
        if "Chrome" in user_agent or "Edge" in user_agent:
            plugins = [
                {
                    "name": "PDF Viewer",
                    "filename": "internal-pdf-viewer",
                    "description": "Portable Document Format"
                },
                {
                    "name": "Chrome PDF Plugin",
                    "filename": "internal-pdf-plugin",
                    "description": "Portable Document Format"
                }
            ]
        elif "Firefox" in user_agent:
            plugins = [
                {
                    "name": "PDF.js",
                    "filename": "pdf.js",
                    "description": "Portable Document Format"
                }
            ]
        
        return plugins
