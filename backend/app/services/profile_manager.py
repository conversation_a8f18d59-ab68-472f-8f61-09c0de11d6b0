"""
Profile Manager Service - Antidetect Browser Profile Management
"""

import os
import json
import uuid
import asyncio
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontex<PERSON>, Page

from ..core.config import settings
from ..core.logger import setup_logger
from ..models.profile import Profile, ProxyType, ProfileStatus, ProxyConfig
from .fingerprint_generator import FingerprintGenerator

logger = setup_logger(__name__)


class AntidetectProfileManager:
    """
    Enhanced Profile Manager with antidetect capabilities
    Built with Playwright for browser automation
    """

    def __init__(self):
        self.fingerprint_generator = FingerprintGenerator()
        self.active_browsers = {}  # profile_id -> browser_instance
        self.playwright = None
        self.browser = None
        
    async def create_profile(self, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new antidetect browser profile
        
        Args:
            profile_data: Profile configuration data
            
        Returns:
            Dict containing profile creation result
        """
        try:
            profile_name = profile_data['name']
            proxy_config = profile_data.get('proxy_config', {})
            
            logger.info(f"Creating profile: {profile_name}")
            
            # Generate unique profile path
            profile_path = settings.PROFILES_DIR / f"{profile_name}_{uuid.uuid4().hex[:8]}"
            profile_path.mkdir(exist_ok=True)
            
            # Generate browser fingerprint
            fingerprint = await self.fingerprint_generator.generate_fingerprint()
            
            # Create browser config with proxy if specified
            browser_config = self._create_browser_config(proxy_config, fingerprint)
            
            # Create profile directory
            actual_profile_path = str(profile_path)
            os.makedirs(actual_profile_path, exist_ok=True)
            
            # Save fingerprint and metadata
            metadata = {
                'fingerprint': fingerprint,
                'proxy_config': proxy_config,
                'created_at': datetime.now().isoformat(),
                'user_agent': fingerprint.get('user_agent'),
                'viewport': fingerprint.get('viewport')
            }
            
            metadata_file = Path(actual_profile_path) / 'metadata.json'
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            logger.info(f"Profile created successfully: {actual_profile_path}")
            
            return {
                'success': True,
                'profile_path': str(actual_profile_path),
                'fingerprint': fingerprint,
                'message': f'Profile {profile_name} created successfully'
            }
            
        except Exception as e:
            logger.error(f"Failed to create profile {profile_name}: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'Failed to create profile: {e}'
            }
    
    def _create_browser_config(self, proxy_config: Dict, fingerprint: Dict) -> Dict:
        """Create browser config with proxy and fingerprint settings"""
        
        config_params = {
            'browser_type': 'chromium',
            'headless': False,
            'user_agent': fingerprint.get('user_agent'),
            'viewport_width': fingerprint.get('viewport', {}).get('width', 1366),
            'viewport_height': fingerprint.get('viewport', {}).get('height', 768),
            'extra_args': [
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        }
        
        # Add proxy configuration if specified
        if proxy_config.get('type') != ProxyType.NO_PROXY and proxy_config.get('host'):
            proxy_url = self._build_proxy_url(proxy_config)
            if proxy_url:
                config_params['proxy_config'] = {
                    'server': proxy_url,
                    'username': proxy_config.get('username'),
                    'password': proxy_config.get('password')
                }
        
        # return BrowserConfig(**config_params)
        return config_params  # Return dict instead of BrowserConfig for now
    
    def _build_proxy_url(self, proxy_config: Dict) -> Optional[str]:
        """Build proxy URL from configuration"""
        proxy_type = proxy_config.get('type')
        host = proxy_config.get('host')
        port = proxy_config.get('port')
        
        if not host or not port:
            return None
            
        if proxy_type == ProxyType.HTTP:
            return f"http://{host}:{port}"
        elif proxy_type == ProxyType.HTTPS:
            return f"https://{host}:{port}"
        elif proxy_type == ProxyType.SOCKS5:
            return f"socks5://{host}:{port}"
        
        return None
    
    async def test_profile(self, profile_path: str, proxy_config: Dict) -> Dict[str, Any]:
        """
        Test profile connectivity and proxy
        
        Args:
            profile_path: Path to the profile directory
            proxy_config: Proxy configuration
            
        Returns:
            Dict containing test results
        """
        try:
            logger.info(f"Testing profile: {profile_path}")
            
            # Load profile metadata
            metadata_file = Path(profile_path) / 'metadata.json'
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                fingerprint = metadata.get('fingerprint', {})
            else:
                fingerprint = await self.fingerprint_generator.generate_fingerprint()
            
            # Create browser config
            browser_config = self._create_browser_config(proxy_config, fingerprint)
            browser_config.user_data_dir = profile_path
            browser_config.use_managed_browser = True
            
            # Test with a simple page
            # async with AsyncWebCrawler(config=browser_config) as crawler:
            # Temporary implementation without crawl4ai
            return {
                'success': True,
                'message': 'Profile test successful (mock)',
                'ip_address': 'Mock IP',
                'user_agent': fingerprint.get('user_agent'),
                'fingerprint': fingerprint
            }
                    
        except Exception as e:
            logger.error(f"Profile test failed: {e}")
            return {
                'success': False,
                'message': f'Profile test failed: {e}',
                'error': str(e)
            }
    
    async def login_facebook(self, profile_path: str, credentials: Dict[str, str]) -> Dict[str, Any]:
        """
        Login to Facebook using the profile
        
        Args:
            profile_path: Path to the profile directory
            credentials: Facebook login credentials
            
        Returns:
            Dict containing login result
        """
        try:
            logger.info(f"Attempting Facebook login for profile: {profile_path}")
            
            # Load profile metadata
            metadata_file = Path(profile_path) / 'metadata.json'
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                fingerprint = metadata.get('fingerprint', {})
                proxy_config = metadata.get('proxy_config', {})
            else:
                fingerprint = await self.fingerprint_generator.generate_fingerprint()
                proxy_config = {}
            
            # Create browser config
            browser_config = self._create_browser_config(proxy_config, fingerprint)
            browser_config.user_data_dir = profile_path
            browser_config.use_managed_browser = True
            browser_config.headless = False  # Must be visible for login
            
            # async with AsyncWebCrawler(config=browser_config) as crawler:
            # Temporary implementation without crawl4ai
            return {
                'success': True,
                'message': 'Facebook login page loaded (mock). Please login manually.',
                'manual_login_required': True
            }
                
        except Exception as e:
            logger.error(f"Facebook login failed: {e}")
            return {
                'success': False,
                'message': f'Facebook login failed: {e}',
                'error': str(e)
            }
    
    async def get_profile_cookies(self, profile_path: str) -> Optional[List[Dict]]:
        """Get cookies from profile"""
        try:
            # Load cookies from profile if available
            cookies_file = Path(profile_path) / 'cookies.json'
            if cookies_file.exists():
                with open(cookies_file, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logger.error(f"Failed to get cookies: {e}")
            return None
    
    async def save_profile_cookies(self, profile_path: str, cookies: List[Dict]) -> bool:
        """Save cookies to profile"""
        try:
            cookies_file = Path(profile_path) / 'cookies.json'
            with open(cookies_file, 'w') as f:
                json.dump(cookies, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Failed to save cookies: {e}")
            return False
    
    async def delete_profile(self, profile_path: str) -> bool:
        """Delete profile directory and all associated data"""
        try:
            import shutil
            if Path(profile_path).exists():
                shutil.rmtree(profile_path)
                logger.info(f"Profile deleted: {profile_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete profile: {e}")
            return False

    async def launch_browser(self, profile_path: str, proxy_config: Dict[str, Any] = None, headless: bool = False) -> Dict[str, Any]:
        """
        Launch browser with profile and proxy configuration
        """
        try:
            if not self.playwright:
                self.playwright = await async_playwright().start()

            # Load profile metadata
            metadata_file = Path(profile_path) / 'metadata.json'
            fingerprint = {}
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                    fingerprint = metadata.get('fingerprint', {})

            # Configure browser launch options
            launch_options = {
                'headless': headless,
                'args': [
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-blink-features=AutomationControlled',
                ]
            }

            # Add proxy if configured
            if proxy_config and proxy_config.get('host'):
                proxy_url = f"{proxy_config.get('type', 'http')}://"
                if proxy_config.get('username'):
                    proxy_url += f"{proxy_config['username']}:{proxy_config['password']}@"
                proxy_url += f"{proxy_config['host']}:{proxy_config['port']}"
                launch_options['proxy'] = {'server': proxy_url}

            # Launch browser
            browser = await self.playwright.chromium.launch(**launch_options)

            # Create context with fingerprint
            context_options = {}

            if fingerprint.get('user_agent'):
                context_options['user_agent'] = fingerprint['user_agent']
            if fingerprint.get('viewport'):
                context_options['viewport'] = fingerprint['viewport']

            context = await browser.new_context(**context_options)

            # Store browser instance
            profile_id = os.path.basename(profile_path)
            self.active_browsers[profile_id] = {
                'browser': browser,
                'context': context,
                'launched_at': datetime.now()
            }

            logger.info(f"Browser launched successfully for profile: {profile_path}")

            return {
                'success': True,
                'profile_id': profile_id,
                'message': 'Browser launched successfully'
            }

        except Exception as e:
            logger.error(f"Browser launch failed: {e}")
            return {
                'success': False,
                'message': f'Browser launch failed: {e}',
                'error': str(e)
            }

    async def open_facebook_login(self, profile_id: str) -> Dict[str, Any]:
        """Open Facebook login page in the browser"""
        try:
            if profile_id not in self.active_browsers:
                return {
                    'success': False,
                    'message': 'Browser not launched for this profile'
                }

            context = self.active_browsers[profile_id]['context']
            page = await context.new_page()

            # Navigate to Facebook login
            await page.goto('https://www.facebook.com/login')

            logger.info(f"Facebook login page opened for profile: {profile_id}")

            return {
                'success': True,
                'message': 'Facebook login page opened. Please complete login manually.',
                'page_url': page.url
            }

        except Exception as e:
            logger.error(f"Failed to open Facebook login: {e}")
            return {
                'success': False,
                'message': f'Failed to open Facebook login: {e}',
                'error': str(e)
            }

    async def complete_facebook_login(self, profile_id: str) -> Dict[str, Any]:
        """Complete Facebook login and save cookies"""
        try:
            if profile_id not in self.active_browsers:
                return {
                    'success': False,
                    'message': 'Browser not launched for this profile'
                }

            context = self.active_browsers[profile_id]['context']
            pages = context.pages

            if not pages:
                return {
                    'success': False,
                    'message': 'No active pages found'
                }

            page = pages[0]  # Use the first page

            # Check if we're logged in by looking for Facebook-specific elements
            try:
                # Wait for either login form or logged-in state
                await page.wait_for_selector('div[role="main"], form[data-testid="royal_login_form"]', timeout=5000)

                # Check if we're on a Facebook page and logged in
                current_url = page.url
                if 'facebook.com' in current_url and 'login' not in current_url:
                    # We're logged in, save cookies
                    cookies = await context.cookies()

                    # Save cookies to profile
                    profile_path = f"/tmp/profiles/{profile_id}"  # This should be improved
                    await self.save_profile_cookies(profile_path, cookies)

                    logger.info(f"Facebook login completed for profile: {profile_id}")

                    return {
                        'success': True,
                        'message': 'Facebook login completed successfully. Cookies saved.',
                        'logged_in': True
                    }
                else:
                    return {
                        'success': False,
                        'message': 'Login not completed. Please complete the login process.',
                        'logged_in': False
                    }

            except Exception as e:
                return {
                    'success': False,
                    'message': 'Unable to determine login status. Please try again.',
                    'error': str(e)
                }

        except Exception as e:
            logger.error(f"Failed to complete Facebook login: {e}")
            return {
                'success': False,
                'message': f'Failed to complete Facebook login: {e}',
                'error': str(e)
            }

    async def close_browser(self, profile_id: str) -> Dict[str, Any]:
        """Close browser for a specific profile"""
        try:
            if profile_id not in self.active_browsers:
                return {
                    'success': False,
                    'message': 'Browser not found for this profile'
                }

            browser_info = self.active_browsers[profile_id]
            browser = browser_info['browser']
            context = browser_info['context']

            # Close context and browser
            await context.close()
            await browser.close()

            # Remove from active browsers
            del self.active_browsers[profile_id]

            logger.info(f"Browser closed for profile: {profile_id}")

            return {
                'success': True,
                'message': 'Browser closed successfully'
            }

        except Exception as e:
            logger.error(f"Failed to close browser: {e}")
            return {
                'success': False,
                'message': f'Failed to close browser: {e}',
                'error': str(e)
            }

    def _build_proxy_url(self, proxy_config: Dict) -> Optional[str]:
        """Build proxy URL from configuration"""
        proxy_type = proxy_config.get('type')
        host = proxy_config.get('host')
        port = proxy_config.get('port')

        if not host or not port:
            return None

        if proxy_type == ProxyType.HTTP:
            return f"http://{host}:{port}"
        elif proxy_type == ProxyType.HTTPS:
            return f"https://{host}:{port}"
        elif proxy_type == ProxyType.SOCKS5:
            return f"socks5://{host}:{port}"

        return None

    async def cleanup(self):
        """Cleanup all active browsers"""
        try:
            for profile_id in list(self.active_browsers.keys()):
                await self.close_browser(profile_id)

            if self.playwright:
                await self.playwright.stop()
                self.playwright = None

        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
