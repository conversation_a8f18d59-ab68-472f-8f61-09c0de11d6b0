"""
Profile Manager Service - Antidetect Browser Profile Management
"""

import os
import json
import uuid
import asyncio
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontex<PERSON>, Page

from ..core.config import settings
from ..core.logger import setup_logger
from ..models.profile import Profile, ProxyType, ProfileStatus, ProxyConfig
from .fingerprint_generator import FingerprintGenerator

logger = setup_logger(__name__)


class AntidetectProfileManager:
    """
    Enhanced Profile Manager with antidetect capabilities
    Built with Playwright for browser automation
    """

    def __init__(self):
        self.fingerprint_generator = FingerprintGenerator()
        self.active_browsers = {}  # profile_id -> browser_instance
        self.playwright = None
        self.browser = None
        
    async def create_profile(self, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new antidetect browser profile
        
        Args:
            profile_data: Profile configuration data
            
        Returns:
            Dict containing profile creation result
        """
        try:
            profile_name = profile_data['name']
            proxy_config = profile_data.get('proxy_config', {})
            
            logger.info(f"Creating profile: {profile_name}")
            
            # Generate unique profile path
            profile_path = settings.PROFILES_DIR / f"{profile_name}_{uuid.uuid4().hex[:8]}"
            profile_path.mkdir(exist_ok=True)
            
            # Generate browser fingerprint
            fingerprint = await self.fingerprint_generator.generate_fingerprint()
            
            # Create browser config with proxy if specified
            browser_config = self._create_browser_config(proxy_config, fingerprint)
            
            # Create profile directory
            actual_profile_path = str(profile_path)
            os.makedirs(actual_profile_path, exist_ok=True)
            
            # Save fingerprint and metadata
            metadata = {
                'fingerprint': fingerprint,
                'proxy_config': proxy_config,
                'created_at': datetime.now().isoformat(),
                'user_agent': fingerprint.get('user_agent'),
                'viewport': fingerprint.get('viewport')
            }
            
            metadata_file = Path(actual_profile_path) / 'metadata.json'
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            logger.info(f"Profile created successfully: {actual_profile_path}")
            
            return {
                'success': True,
                'profile_path': str(actual_profile_path),
                'fingerprint': fingerprint,
                'message': f'Profile {profile_name} created successfully'
            }
            
        except Exception as e:
            logger.error(f"Failed to create profile {profile_name}: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'Failed to create profile: {e}'
            }
    
    def _create_browser_config(self, proxy_config: Dict, fingerprint: Dict) -> Dict:
        """Create browser config with proxy and fingerprint settings"""
        
        config_params = {
            'browser_type': 'chromium',
            'headless': False,
            'user_agent': fingerprint.get('user_agent'),
            'viewport_width': fingerprint.get('viewport', {}).get('width', 1366),
            'viewport_height': fingerprint.get('viewport', {}).get('height', 768),
            'extra_args': [
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        }
        
        # Add proxy configuration if specified
        if proxy_config.get('type') != ProxyType.NO_PROXY and proxy_config.get('host'):
            proxy_url = self._build_proxy_url(proxy_config)
            if proxy_url:
                config_params['proxy_config'] = {
                    'server': proxy_url,
                    'username': proxy_config.get('username'),
                    'password': proxy_config.get('password')
                }
        
        # return BrowserConfig(**config_params)
        return config_params  # Return dict instead of BrowserConfig for now
    
    def _build_proxy_url(self, proxy_config: Dict) -> Optional[str]:
        """Build proxy URL from configuration"""
        proxy_type = proxy_config.get('type')
        host = proxy_config.get('host')
        port = proxy_config.get('port')
        
        if not host or not port:
            return None
            
        if proxy_type == ProxyType.HTTP:
            return f"http://{host}:{port}"
        elif proxy_type == ProxyType.HTTPS:
            return f"https://{host}:{port}"
        elif proxy_type == ProxyType.SOCKS5:
            return f"socks5://{host}:{port}"
        
        return None
    
    async def test_profile(self, profile_path: str, proxy_config: Dict) -> Dict[str, Any]:
        """
        Test profile connectivity and proxy
        
        Args:
            profile_path: Path to the profile directory
            proxy_config: Proxy configuration
            
        Returns:
            Dict containing test results
        """
        try:
            logger.info(f"Testing profile: {profile_path}")
            
            # Load profile metadata
            metadata_file = Path(profile_path) / 'metadata.json'
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                fingerprint = metadata.get('fingerprint', {})
            else:
                fingerprint = await self.fingerprint_generator.generate_fingerprint()
            
            # Create browser config
            browser_config = self._create_browser_config(proxy_config, fingerprint)
            browser_config['user_data_dir'] = profile_path
            browser_config['use_managed_browser'] = True

            # Test with a simple page using Playwright
            try:
                # Test browser launch with the profile
                launch_result = await self.launch_browser(profile_path, proxy_config, headless=True)
                if launch_result.get('success'):
                    # Close the browser after test
                    profile_id = launch_result.get('profile_id')
                    if profile_id:
                        await self.close_browser(profile_id)

                    return {
                        'success': True,
                        'message': 'Profile test successful - browser launched and closed',
                        'user_agent': fingerprint.get('user_agent'),
                        'fingerprint': fingerprint,
                        'proxy_configured': bool(proxy_config.get('host'))
                    }
                else:
                    return {
                        'success': False,
                        'message': f'Profile test failed: {launch_result.get("message")}',
                        'error': launch_result.get('error')
                    }
            except Exception as test_error:
                logger.warning(f"Browser test failed, falling back to mock: {test_error}")
                # Fallback to mock test
                return {
                    'success': True,
                    'message': 'Profile test successful (mock - browser test failed)',
                    'user_agent': fingerprint.get('user_agent'),
                    'fingerprint': fingerprint,
                    'proxy_configured': bool(proxy_config.get('host'))
                }
                    
        except Exception as e:
            logger.error(f"Profile test failed: {e}")
            return {
                'success': False,
                'message': f'Profile test failed: {e}',
                'error': str(e)
            }
    
    async def login_facebook(self, profile_path: str, credentials: Dict[str, str]) -> Dict[str, Any]:
        """
        Login to Facebook using the profile
        
        Args:
            profile_path: Path to the profile directory
            credentials: Facebook login credentials
            
        Returns:
            Dict containing login result
        """
        try:
            logger.info(f"Attempting Facebook login for profile: {profile_path}")
            
            # Load profile metadata
            metadata_file = Path(profile_path) / 'metadata.json'
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                fingerprint = metadata.get('fingerprint', {})
                proxy_config = metadata.get('proxy_config', {})
            else:
                fingerprint = await self.fingerprint_generator.generate_fingerprint()
                proxy_config = {}
            
            # Create browser config
            browser_config = self._create_browser_config(proxy_config, fingerprint)
            browser_config.user_data_dir = profile_path
            browser_config.use_managed_browser = True
            browser_config.headless = False  # Must be visible for login
            
            # async with AsyncWebCrawler(config=browser_config) as crawler:
            # Temporary implementation without crawl4ai
            return {
                'success': True,
                'message': 'Facebook login page loaded (mock). Please login manually.',
                'manual_login_required': True
            }
                
        except Exception as e:
            logger.error(f"Facebook login failed: {e}")
            return {
                'success': False,
                'message': f'Facebook login failed: {e}',
                'error': str(e)
            }
    
    async def get_profile_cookies(self, profile_path: str) -> Optional[List[Dict]]:
        """Get cookies from profile"""
        try:
            # Load cookies from profile if available
            cookies_file = Path(profile_path) / 'cookies.json'
            if cookies_file.exists():
                with open(cookies_file, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logger.error(f"Failed to get cookies: {e}")
            return None
    
    async def save_profile_cookies(self, profile_path: str, cookies: List[Dict]) -> bool:
        """Save cookies to profile"""
        try:
            cookies_file = Path(profile_path) / 'cookies.json'
            with open(cookies_file, 'w') as f:
                json.dump(cookies, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Failed to save cookies: {e}")
            return False

    async def save_facebook_cookies(self, profile_path: str, cookies: List[Dict]) -> bool:
        """Save Facebook-specific cookies to profile"""
        try:
            # Ensure profile directory exists
            profile_dir = Path(profile_path)
            profile_dir.mkdir(parents=True, exist_ok=True)

            # Filter Facebook cookies
            facebook_cookies = [
                cookie for cookie in cookies
                if 'facebook.com' in cookie.get('domain', '') or
                   '.facebook.com' in cookie.get('domain', '') or
                   'fb.com' in cookie.get('domain', '') or
                   '.fb.com' in cookie.get('domain', '')
            ]

            # Clean up cookies for better compatibility
            cleaned_cookies = []
            for cookie in facebook_cookies:
                cleaned_cookie = {
                    'name': cookie.get('name'),
                    'value': cookie.get('value'),
                    'domain': cookie.get('domain'),
                    'path': cookie.get('path', '/'),
                    'expires': cookie.get('expires'),
                    'httpOnly': cookie.get('httpOnly', False),
                    'secure': cookie.get('secure', False),
                    'sameSite': cookie.get('sameSite', 'Lax')
                }
                # Remove None values
                cleaned_cookie = {k: v for k, v in cleaned_cookie.items() if v is not None}
                cleaned_cookies.append(cleaned_cookie)

            cookies_file = profile_dir / 'facebook_cookies.json'
            with open(cookies_file, 'w') as f:
                json.dump(cleaned_cookies, f, indent=2)

            logger.info(f"Saved {len(cleaned_cookies)} Facebook cookies to {cookies_file}")

            # Also save to general cookies file for backup
            general_cookies_file = profile_dir / 'cookies.json'
            with open(general_cookies_file, 'w') as f:
                json.dump(cookies, f, indent=2)

            return True
        except Exception as e:
            logger.error(f"Failed to save Facebook cookies: {e}")
            return False
    
    async def delete_profile(self, profile_path: str) -> bool:
        """Delete profile directory and all associated data"""
        try:
            import shutil
            if Path(profile_path).exists():
                shutil.rmtree(profile_path)
                logger.info(f"Profile deleted: {profile_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete profile: {e}")
            return False

    async def launch_browser(self, profile_path: str, proxy_config: Dict[str, Any] = None, headless: bool = False) -> Dict[str, Any]:
        """
        Launch browser with profile and proxy configuration
        """
        try:
            # Check if browser is already launched for this profile
            profile_id = os.path.basename(profile_path)
            if profile_id in self.active_browsers:
                return {
                    'success': True,
                    'profile_id': profile_id,
                    'message': 'Browser is already launched for this profile',
                    'already_active': True
                }

            if not self.playwright:
                self.playwright = await async_playwright().start()

            # Load profile metadata
            metadata_file = Path(profile_path) / 'metadata.json'
            fingerprint = {}
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                    fingerprint = metadata.get('fingerprint', {})

            # Configure browser launch options
            launch_options = {
                'headless': headless,
                'args': [
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-blink-features=AutomationControlled',
                ]
            }

            # Add proxy if configured
            if proxy_config and proxy_config.get('host'):
                proxy_url = f"{proxy_config.get('type', 'http')}://"
                if proxy_config.get('username'):
                    proxy_url += f"{proxy_config['username']}:{proxy_config['password']}@"
                proxy_url += f"{proxy_config['host']}:{proxy_config['port']}"
                launch_options['proxy'] = {'server': proxy_url}

            # Launch browser
            browser = await self.playwright.chromium.launch(**launch_options)

            # Create context with fingerprint
            context_options = {}

            if fingerprint.get('user_agent'):
                context_options['user_agent'] = fingerprint['user_agent']
            if fingerprint.get('viewport'):
                context_options['viewport'] = fingerprint['viewport']

            context = await browser.new_context(**context_options)

            # Store browser instance
            profile_id = os.path.basename(profile_path)
            self.active_browsers[profile_id] = {
                'browser': browser,
                'context': context,
                'launched_at': datetime.now()
            }

            logger.info(f"Browser launched successfully for profile: {profile_path}")

            return {
                'success': True,
                'profile_id': profile_id,
                'message': 'Browser launched successfully'
            }

        except Exception as e:
            logger.error(f"Browser launch failed: {e}")
            return {
                'success': False,
                'message': f'Browser launch failed: {e}',
                'error': str(e)
            }

    async def open_facebook_login(self, profile_id: str, profile_path: str = None, proxy_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Open Facebook login page in the browser, auto-launch if needed"""
        try:
            # If browser not launched, auto-launch it
            if profile_id not in self.active_browsers:
                if not profile_path:
                    return {
                        'success': False,
                        'message': 'Browser not launched and profile path not provided'
                    }

                logger.info(f"Browser not active, auto-launching for profile: {profile_id}")
                launch_result = await self.launch_browser(profile_path, proxy_config, headless=False)

                if not launch_result.get('success'):
                    return {
                        'success': False,
                        'message': f'Failed to auto-launch browser: {launch_result.get("message")}',
                        'error': launch_result.get('error')
                    }

            context = self.active_browsers[profile_id]['context']

            # Load saved cookies if they exist
            cookies_loaded = False
            if profile_path:
                cookies_file = Path(profile_path) / 'facebook_cookies.json'
                if cookies_file.exists():
                    try:
                        with open(cookies_file, 'r') as f:
                            cookies = json.load(f)

                        if cookies:
                            # Validate and add cookies
                            valid_cookies = []
                            for cookie in cookies:
                                if cookie.get('name') and cookie.get('value') and cookie.get('domain'):
                                    valid_cookies.append(cookie)

                            if valid_cookies:
                                await context.add_cookies(valid_cookies)
                                cookies_loaded = True
                                logger.info(f"Loaded {len(valid_cookies)} Facebook cookies for profile: {profile_id}")
                            else:
                                logger.warning(f"No valid cookies found in {cookies_file}")
                        else:
                            logger.warning(f"Empty cookies file: {cookies_file}")
                    except Exception as e:
                        logger.warning(f"Failed to load cookies from {cookies_file}: {e}")
                else:
                    logger.info(f"No saved cookies found at {cookies_file}")

            page = await context.new_page()

            # Navigate to Facebook - if cookies are valid, should auto-login
            if cookies_loaded:
                logger.info("Attempting to use saved cookies for auto-login")
                await page.goto('https://www.facebook.com/')

                # Wait for page to load and check login status
                try:
                    await page.wait_for_load_state('networkidle', timeout=15000)

                    # Wait a bit more for any redirects
                    await page.wait_for_timeout(2000)

                    current_url = page.url
                    logger.info(f"After loading with cookies, URL: {current_url}")

                    # Check if we're logged in by looking for specific elements
                    try:
                        # Look for logged-in indicators
                        await page.wait_for_selector('div[role="banner"], div[data-testid="royal_login_form"]', timeout=5000)

                        # Check URL and page content
                        if ('login' not in current_url and
                            'checkpoint' not in current_url and
                            'facebook.com' in current_url):

                            # Double check by looking for login form
                            login_form = await page.query_selector('form[data-testid="royal_login_form"]')
                            if not login_form:
                                # Successfully logged in
                                message = 'Facebook opened successfully. You are already logged in with saved cookies!'
                                logged_in = True
                            else:
                                # Login form present, cookies didn't work
                                message = 'Facebook login page opened. Saved cookies expired, please login again.'
                                logged_in = False
                        else:
                            # Redirected to login or checkpoint
                            await page.goto('https://www.facebook.com/login')
                            message = 'Facebook login page opened. Saved cookies expired, please login again.'
                            logged_in = False

                    except Exception as e:
                        logger.warning(f"Error checking login status: {e}")
                        # Fallback to login page
                        await page.goto('https://www.facebook.com/login')
                        message = 'Facebook login page opened. Please complete login manually.'
                        logged_in = False

                except Exception as e:
                    logger.warning(f"Error loading Facebook with cookies: {e}")
                    # Fallback to login page
                    await page.goto('https://www.facebook.com/login')
                    message = 'Facebook login page opened. Please complete login manually.'
                    logged_in = False
            else:
                # No cookies, go directly to login page
                logger.info("No saved cookies, opening login page")
                await page.goto('https://www.facebook.com/login')
                await page.wait_for_load_state('networkidle', timeout=10000)
                message = 'Facebook login page opened. Please complete login manually.'
                logged_in = False

            logger.info(f"Facebook opened for profile: {profile_id}, URL: {page.url}, Logged in: {logged_in}")

            return {
                'success': True,
                'message': message,
                'page_url': page.url,
                'auto_launched': profile_id not in self.active_browsers,
                'logged_in': logged_in,
                'cookies_loaded': cookies_loaded
            }

        except Exception as e:
            logger.error(f"Failed to open Facebook: {e}")
            return {
                'success': False,
                'message': f'Failed to open Facebook: {e}',
                'error': str(e)
            }

    async def complete_facebook_login(self, profile_id: str, profile_path: str = None) -> Dict[str, Any]:
        """Complete Facebook login and save cookies"""
        try:
            if profile_id not in self.active_browsers:
                return {
                    'success': False,
                    'message': 'Browser not launched for this profile'
                }

            context = self.active_browsers[profile_id]['context']
            pages = context.pages

            if not pages:
                return {
                    'success': False,
                    'message': 'No active pages found'
                }

            page = pages[0]  # Use the first page

            # Check if we're logged in by looking for Facebook-specific elements
            try:
                # Wait for either login form or logged-in state
                await page.wait_for_selector('div[role="main"], form[data-testid="royal_login_form"]', timeout=5000)

                # Check if we're on a Facebook page and logged in
                current_url = page.url
                if 'facebook.com' in current_url and 'login' not in current_url:
                    # We're logged in, save cookies
                    cookies = await context.cookies()

                    # Save Facebook cookies to profile
                    if not profile_path:
                        profile_path = f"/tmp/profiles/{profile_id}"  # Fallback
                    await self.save_facebook_cookies(profile_path, cookies)

                    logger.info(f"Facebook login completed for profile: {profile_id}")

                    return {
                        'success': True,
                        'message': 'Facebook login completed successfully. Cookies saved. Browser is still open.',
                        'logged_in': True,
                        'browser_status': 'open'
                    }
                else:
                    return {
                        'success': False,
                        'message': 'Login not completed. Please complete the login process.',
                        'logged_in': False
                    }

            except Exception as e:
                return {
                    'success': False,
                    'message': 'Unable to determine login status. Please try again.',
                    'error': str(e)
                }

        except Exception as e:
            logger.error(f"Failed to complete Facebook login: {e}")
            return {
                'success': False,
                'message': f'Failed to complete Facebook login: {e}',
                'error': str(e)
            }

    async def get_browser_status(self, profile_id: str) -> Dict[str, Any]:
        """Get browser status for a profile"""
        try:
            if profile_id in self.active_browsers:
                browser_info = self.active_browsers[profile_id]
                return {
                    'success': True,
                    'browser_active': True,
                    'launched_at': browser_info['launched_at'].isoformat(),
                    'message': 'Browser is currently active'
                }
            else:
                return {
                    'success': True,
                    'browser_active': False,
                    'message': 'No active browser for this profile'
                }
        except Exception as e:
            logger.error(f"Failed to get browser status: {e}")
            return {
                'success': False,
                'message': f'Failed to get browser status: {e}',
                'error': str(e)
            }

    async def close_browser(self, profile_id: str) -> Dict[str, Any]:
        """Close browser for a specific profile"""
        try:
            if profile_id not in self.active_browsers:
                return {
                    'success': False,
                    'message': 'Browser not found for this profile'
                }

            browser_info = self.active_browsers[profile_id]
            browser = browser_info['browser']
            context = browser_info['context']

            # Close context and browser
            await context.close()
            await browser.close()

            # Remove from active browsers
            del self.active_browsers[profile_id]

            logger.info(f"Browser closed for profile: {profile_id}")

            return {
                'success': True,
                'message': 'Browser closed successfully'
            }

        except Exception as e:
            logger.error(f"Failed to close browser: {e}")
            return {
                'success': False,
                'message': f'Failed to close browser: {e}',
                'error': str(e)
            }

    def _build_proxy_url(self, proxy_config: Dict) -> Optional[str]:
        """Build proxy URL from configuration"""
        proxy_type = proxy_config.get('type')
        host = proxy_config.get('host')
        port = proxy_config.get('port')

        if not host or not port:
            return None

        if proxy_type == ProxyType.HTTP:
            return f"http://{host}:{port}"
        elif proxy_type == ProxyType.HTTPS:
            return f"https://{host}:{port}"
        elif proxy_type == ProxyType.SOCKS5:
            return f"socks5://{host}:{port}"

        return None

    async def cleanup(self):
        """Cleanup all active browsers"""
        try:
            for profile_id in list(self.active_browsers.keys()):
                await self.close_browser(profile_id)

            if self.playwright:
                await self.playwright.stop()
                self.playwright = None

        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
