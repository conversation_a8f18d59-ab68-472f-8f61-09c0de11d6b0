"""
Scraping models for Facebook Automation
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

from ..core.database import Base


class ScrapingTaskStatus(str, Enum):
    """Scraping task status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ScrapingType(str, Enum):
    """Types of scraping"""
    COMMENTS = "comments"
    LIKES = "likes"
    SHARES = "shares"
    ALL = "all"


# SQLAlchemy Models
class ScrapingTask(Base):
    """Scraping task database model"""
    __tablename__ = "scraping_tasks"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(255), unique=True, index=True, nullable=False)
    profile_id = Column(Integer, ForeignKey("profiles.id"), nullable=False)
    
    # Task configuration
    target_url = Column(String(1000), nullable=False)
    scraping_types = Column(JSON, nullable=False)  # List of ScrapingType
    max_results = Column(Integer, default=1000)
    
    # Status and progress
    status = Column(String(50), default=ScrapingTaskStatus.PENDING)
    progress = Column(Float, default=0.0)  # 0.0 to 100.0
    total_found = Column(Integer, default=0)
    total_scraped = Column(Integer, default=0)
    
    # Results
    results_file_path = Column(String(500), nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    scraped_users = relationship("ScrapedUser", back_populates="task", cascade="all, delete-orphan")


class ScrapedUser(Base):
    """Scraped user data model"""
    __tablename__ = "scraped_users"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("scraping_tasks.id"), nullable=False)
    
    # User information
    facebook_uid = Column(String(255), nullable=False, index=True)
    full_name = Column(String(500), nullable=True)
    profile_url = Column(String(1000), nullable=True)
    gender = Column(String(50), nullable=True)
    profile_picture_url = Column(String(1000), nullable=True)
    
    # Interaction data
    interaction_type = Column(String(50), nullable=False)  # comment, like, share
    interaction_content = Column(Text, nullable=True)  # For comments
    interaction_timestamp = Column(DateTime, nullable=True)
    
    # Additional metadata
    extra_data = Column(JSON, nullable=True)
    
    # Timestamps
    scraped_at = Column(DateTime, server_default=func.now())
    
    # Relationships
    task = relationship("ScrapingTask", back_populates="scraped_users")


# Pydantic Models for API
class ScrapingConfig(BaseModel):
    """Scraping configuration"""
    target_url: str = Field(..., min_length=1)
    scraping_types: List[ScrapingType] = [ScrapingType.ALL]
    max_results: int = Field(default=1000, ge=1, le=10000)
    profile_id: int = Field(..., gt=0)


class ScrapingTaskCreate(BaseModel):
    """Scraping task creation model"""
    config: ScrapingConfig


class ScrapingTaskResponse(BaseModel):
    """Scraping task response model"""
    id: int
    task_id: str
    profile_id: int
    target_url: str
    scraping_types: List[str]
    max_results: int
    status: ScrapingTaskStatus
    progress: float
    total_found: int
    total_scraped: int
    results_file_path: Optional[str]
    error_message: Optional[str]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]

    class Config:
        from_attributes = True


class ScrapedUserResponse(BaseModel):
    """Scraped user response model"""
    id: int
    facebook_uid: str
    full_name: Optional[str]
    profile_url: Optional[str]
    gender: Optional[str]
    profile_picture_url: Optional[str]
    interaction_type: str
    interaction_content: Optional[str]
    interaction_timestamp: Optional[datetime]
    scraped_at: datetime

    class Config:
        from_attributes = True


class ScrapingResults(BaseModel):
    """Scraping results summary"""
    task_id: str
    status: ScrapingTaskStatus
    total_users: int
    users_by_type: Dict[str, int]
    users: List[ScrapedUserResponse]


class ExportRequest(BaseModel):
    """Export request model"""
    task_id: str
    format: str = Field(default="excel", pattern="^(excel|csv)$")
    include_fields: Optional[List[str]] = None
