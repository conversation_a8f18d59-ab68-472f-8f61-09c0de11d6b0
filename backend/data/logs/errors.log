2025-07-10 14:04:35 | ERROR    | app.core.database:init_db:67 - Failed to initialize database: the greenlet library is required to use this function. No module named 'greenlet'
2025-07-10 14:06:03 | ERROR    | main:lifespan:47 - Failed to initialize crawl4ai: cannot import name 'AsyncWebCrawler' from 'crawl4ai' (unknown location)
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:19 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:19 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:35:59 | ERROR    | app.services.profile_manager:create_profile:88 - Failed to create profile Test Workflow Profile: empty range for randrange() (20, 19, -1)
2025-07-10 15:35:59 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to create profile: empty range for randrange() (20, 19, -1)
2025-07-10 15:37:12 | ERROR    | app.services.profile_manager:launch_browser:342 - Browser launch failed: new_context() got an unexpected keyword argument 'user_data_dir'
2025-07-10 15:37:12 | ERROR    | app.core.database:get_db:78 - Database session error: 404: Profile not found
2025-07-10 16:15:32 | ERROR    | app.services.profile_manager:save_profile_cookies:260 - Failed to save cookies: [Errno 2] No such file or directory: '/tmp/profiles/test_a71c6e53/cookies.json'
2025-07-10 16:15:36 | ERROR    | app.services.profile_manager:test_profile:185 - Profile test failed: 'dict' object has no attribute 'user_data_dir'
2025-07-10 16:15:58 | ERROR    | app.services.profile_manager:test_profile:185 - Profile test failed: 'dict' object has no attribute 'user_data_dir'
2025-07-10 16:16:11 | ERROR    | app.services.profile_manager:test_profile:185 - Profile test failed: 'dict' object has no attribute 'user_data_dir'
2025-07-10 16:16:40 | ERROR    | app.services.profile_manager:test_profile:185 - Profile test failed: 'dict' object has no attribute 'user_data_dir'
2025-07-10 16:19:57 | ERROR    | app.core.database:get_db:78 - Database session error: 404: Profile not found
2025-07-10 16:26:56 | ERROR    | app.services.profile_manager:save_profile_cookies:283 - Failed to save cookies: [Errno 2] No such file or directory: '/tmp/profiles/test_875c315a/cookies.json'
2025-07-10 16:33:52 | ERROR    | app.services.profile_manager:save_profile_cookies:283 - Failed to save cookies: [Errno 2] No such file or directory: '/tmp/profiles/test2_addfded4/cookies.json'
2025-07-10 16:33:52 | ERROR    | app.services.profile_manager:save_profile_cookies:283 - Failed to save cookies: [Errno 2] No such file or directory: '/tmp/profiles/test2_addfded4/cookies.json'
2025-07-10 16:35:15 | ERROR    | app.services.profile_manager:save_profile_cookies:283 - Failed to save cookies: [Errno 2] No such file or directory: '/tmp/profiles/test2_addfded4/cookies.json'
2025-07-10 16:43:28 | ERROR    | app.services.profile_manager:save_facebook_cookies:303 - Failed to save Facebook cookies: [Errno 2] No such file or directory: '/tmp/profiles/test_a362556f/facebook_cookies.json'
2025-07-10 16:43:28 | ERROR    | app.services.profile_manager:save_facebook_cookies:303 - Failed to save Facebook cookies: [Errno 2] No such file or directory: '/tmp/profiles/test_a362556f/facebook_cookies.json'
2025-07-10 16:53:40 | ERROR    | app.services.profile_manager:open_facebook_login:557 - Failed to open Facebook: Page.goto: Target page, context or browser has been closed
2025-07-10 16:54:01 | ERROR    | app.services.profile_manager:open_facebook_login:557 - Failed to open Facebook: Page.goto: Target page, context or browser has been closed
2025-07-10 16:54:12 | ERROR    | app.services.profile_manager:open_facebook_login:557 - Failed to open Facebook: Page.goto: Target page, context or browser has been closed
