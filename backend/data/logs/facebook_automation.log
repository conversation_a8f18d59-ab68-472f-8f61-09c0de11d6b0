2025-07-10 14:04:35 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:04:35 | ERROR    | app.core.database:init_db:67 - Failed to initialize database: the greenlet library is required to use this function. No module named 'greenlet'
2025-07-10 14:06:03 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:06:03 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:06:03 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:06:03 | ERROR    | main:lifespan:47 - Failed to initialize crawl4ai: cannot import name 'AsyncWebCrawler' from 'crawl4ai' (unknown location)
2025-07-10 14:07:33 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:07:33 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:07:33 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:07:33 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:08:36 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:08:36 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:08:36 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:08:36 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:08:40 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:08:40 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:08:40 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:08:40 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:10:03 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:10:03 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:10:03 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:10:03 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:10:07 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:10:07 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:10:07 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:10:07 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:11:37 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:11:37 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:11:37 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:11:37 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:11:40 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:11:40 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:11:40 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:11:40 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:12:48 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:12:48 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:12:48 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:12:48 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:12:52 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:12:52 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:12:52 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:12:52 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:12:52 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:12:52 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:12:52 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:12:52 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:12:52 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:13:56 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:13:56 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:13:56 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:13:56 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:13:58 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:13:58 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:13:58 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:13:58 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:13:59 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:13:59 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:13:59 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:13:59 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:13:59 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:14:58 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:14:58 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:14:58 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:14:58 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:15:01 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:15:01 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:15:01 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:15:01 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:15:01 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:15:01 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:15:01 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:15:01 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:15:01 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:12 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:12 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:25 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:25 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:25 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:25 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:27 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:27 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:27 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:27 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:27 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:27 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:27 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:27 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:27 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:37 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:37 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:51 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:51 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:51 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:51 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:54 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:54 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:54 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:54 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:54 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:54 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:54 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:54 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:54 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:20:56 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:20:56 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:21:12 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:21:12 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:21:12 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:21:12 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:23:06 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:23:06 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:23:06 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:23:06 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:23:09 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:23:09 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:23:09 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:23:09 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:23:49 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:23:49 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:23:49 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:23:49 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:23:52 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:23:52 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:23:52 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:23:52 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:24:52 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:24:52 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:24:58 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:24:58 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:24:58 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:24:58 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:25:01 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:25:01 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:25:01 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:25:01 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:36:27 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:36:27 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:36:32 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:36:32 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:36:32 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:36:32 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:36:35 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:36:35 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:36:35 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:36:35 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:42:12 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:42:12 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:45:49 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:45:49 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:45:49 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:45:49 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:46:59 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:47:19 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:47:19 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:47:19 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:47:19 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:56:12 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:56:12 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:56:12 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:56:12 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:58:16 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:03:51 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:03:51 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:03:51 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:03:51 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:10:17 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:10:21 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:10:21 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:10:21 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:10:21 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:10:25 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:10:25 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:10:25 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:10:25 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:10:42 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:10:42 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:10:50 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:10:50 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:10:50 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:10:50 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:02 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:31:03 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:31:03 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:31:03 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:31:03 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:31:30 | INFO     | __main__:main:24 - Initializing database...
2025-07-10 15:31:30 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:31:30 | INFO     | __main__:main:29 - Database initialization completed successfully!
2025-07-10 15:31:30 | INFO     | __main__:main:34 - Database connection test successful
2025-07-10 15:31:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:45 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:31:51 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:31:51 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:31:51 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:31:51 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:32:19 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:19 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:37 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:32:38 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:32:38 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:32:38 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:32:38 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:33:02 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:33:03 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:33:03 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:33:03 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:33:03 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:33:22 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:33:23 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:33:23 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:33:23 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:33:23 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:33:42 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:33:43 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:33:43 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:33:43 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:33:43 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:34:02 | INFO     | __main__:main:24 - Initializing database...
2025-07-10 15:34:02 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:34:02 | INFO     | __main__:main:29 - Database initialization completed successfully!
2025-07-10 15:34:02 | INFO     | __main__:main:34 - Database connection test successful
2025-07-10 15:34:18 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:34:18 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:34:18 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:34:18 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:34:58 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Test Profile
2025-07-10 15:34:58 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Profile_dc800a19
2025-07-10 15:34:58 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: Test Profile
2025-07-10 15:35:59 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Test Workflow Profile
2025-07-10 15:35:59 | ERROR    | app.services.profile_manager:create_profile:88 - Failed to create profile Test Workflow Profile: empty range for randrange() (20, 19, -1)
2025-07-10 15:35:59 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to create profile: empty range for randrange() (20, 19, -1)
2025-07-10 15:36:58 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:36:59 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:36:59 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:36:59 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:36:59 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:37:11 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Test Workflow Profile
2025-07-10 15:37:11 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Workflow Profile_ac9ff127
2025-07-10 15:37:11 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: Test Workflow Profile
2025-07-10 15:37:11 | INFO     | app.api.routes.profiles:update_profile:176 - Profile updated successfully: 2
2025-07-10 15:37:12 | ERROR    | app.services.profile_manager:launch_browser:342 - Browser launch failed: new_context() got an unexpected keyword argument 'user_data_dir'
2025-07-10 15:37:12 | INFO     | app.services.profile_manager:delete_profile:269 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Workflow Profile_ac9ff127
2025-07-10 15:37:12 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 2
2025-07-10 15:37:12 | ERROR    | app.core.database:get_db:78 - Database session error: 404: Profile not found
2025-07-10 15:37:22 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:37:23 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:37:23 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:37:23 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:37:23 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
