2025-07-10 14:04:35 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:04:35 | ERROR    | app.core.database:init_db:67 - Failed to initialize database: the greenlet library is required to use this function. No module named 'greenlet'
2025-07-10 14:06:03 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:06:03 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:06:03 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:06:03 | ERROR    | main:lifespan:47 - Failed to initialize crawl4ai: cannot import name 'AsyncWebCrawler' from 'crawl4ai' (unknown location)
2025-07-10 14:07:33 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:07:33 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:07:33 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:07:33 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:08:36 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:08:36 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:08:36 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:08:36 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:08:40 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:08:40 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:08:40 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:08:40 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:10:03 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:10:03 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:10:03 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:10:03 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:10:07 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:10:07 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:10:07 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:10:07 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:11:37 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:11:37 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:11:37 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:11:37 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:11:40 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:11:40 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:11:40 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:11:40 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:12:48 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:12:48 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:12:48 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:12:48 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:12:52 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:12:52 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:12:52 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:12:52 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:12:52 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:12:52 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:12:52 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:12:52 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:12:52 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:13:56 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:13:56 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:13:56 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:13:56 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:13:58 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:13:58 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:13:58 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:13:58 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:13:59 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:13:59 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:13:59 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:13:59 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:13:59 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:14:58 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:14:58 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:14:58 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:14:58 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:15:01 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:15:01 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:15:01 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:15:01 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:15:01 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:15:01 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:15:01 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:15:01 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:15:01 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:12 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:12 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:25 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:25 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:25 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:25 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:27 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:27 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:27 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:27 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:27 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:27 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:27 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:27 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:27 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:37 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:37 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:18:51 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:51 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:51 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:51 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:54 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:54 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:54 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:54 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:54 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:18:54 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:18:54 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:18:54 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:18:54 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:20:56 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:20:56 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:21:12 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:21:12 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:21:12 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:21:12 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:23:06 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:23:06 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:23:06 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:23:06 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:23:09 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:23:09 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:23:09 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:23:09 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:23:49 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:23:49 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:23:49 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:23:49 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:23:52 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:23:52 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:23:52 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:23:52 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:24:52 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:24:52 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:24:58 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:24:58 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:24:58 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:24:58 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:25:01 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:25:01 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:25:01 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:25:01 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:36:27 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:36:27 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:36:32 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:36:32 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:36:32 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:36:32 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:36:35 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:36:35 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:36:35 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:36:35 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:42:12 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:42:12 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:45:49 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:45:49 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:45:49 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:45:49 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:46:59 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 14:47:19 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:47:19 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:47:19 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:47:19 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:56:12 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 14:56:12 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 14:56:12 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 14:56:12 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 14:58:16 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:03:51 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:03:51 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:03:51 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:03:51 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:10:17 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:10:21 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:10:21 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:10:21 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:10:21 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:10:25 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:10:25 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:10:25 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:10:25 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:10:42 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:10:42 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:10:50 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:10:50 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:10:50 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:10:50 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:21:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:20 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:50 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:22:51 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:21 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:37 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:23:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:24:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:22 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:25:52 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:26:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:27:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:28:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:29:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:30:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:02 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:31:03 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:31:03 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:31:03 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:31:03 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:31:30 | INFO     | __main__:main:24 - Initializing database...
2025-07-10 15:31:30 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:31:30 | INFO     | __main__:main:29 - Database initialization completed successfully!
2025-07-10 15:31:30 | INFO     | __main__:main:34 - Database connection test successful
2025-07-10 15:31:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:31:45 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:31:51 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:31:51 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:31:51 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:31:51 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:32:19 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:19 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.api.routes.scraping:get_scraping_tasks:38 - Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get scraping tasks: (sqlite3.OperationalError) no such table: scraping_tasks
[SQL: SELECT scraping_tasks.id, scraping_tasks.task_id, scraping_tasks.profile_id, scraping_tasks.target_url, scraping_tasks.scraping_types, scraping_tasks.max_results, scraping_tasks.status, scraping_tasks.progress, scraping_tasks.total_found, scraping_tasks.total_scraped, scraping_tasks.results_file_path, scraping_tasks.error_message, scraping_tasks.created_at, scraping_tasks.started_at, scraping_tasks.completed_at, scraping_tasks.updated_at 
FROM scraping_tasks ORDER BY scraping_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.api.routes.messaging:get_messaging_tasks:41 - Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.api.routes.profiles:get_profiles:42 - Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get messaging tasks: (sqlite3.OperationalError) no such table: messaging_tasks
[SQL: SELECT messaging_tasks.id, messaging_tasks.task_id, messaging_tasks.name, messaging_tasks.sender_profile_ids, messaging_tasks.recipient_list_file, messaging_tasks.message_template, messaging_tasks.message_type, messaging_tasks.image_paths, messaging_tasks.concurrent_threads, messaging_tasks.messages_per_account_min, messaging_tasks.messages_per_account_max, messaging_tasks.delay_between_messages_min, messaging_tasks.delay_between_messages_max, messaging_tasks.avoid_duplicate_uids, messaging_tasks.randomize_message, messaging_tasks.status, messaging_tasks.progress, messaging_tasks.total_recipients, messaging_tasks.messages_sent, messaging_tasks.messages_failed, messaging_tasks.messages_skipped, messaging_tasks.error_message, messaging_tasks.created_at, messaging_tasks.started_at, messaging_tasks.completed_at, messaging_tasks.updated_at 
FROM messaging_tasks ORDER BY messaging_tasks.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:33 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to get profiles: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT profiles.id, profiles.name, profiles.profile_path, profiles.proxy_type, profiles.proxy_host, profiles.proxy_port, profiles.proxy_username, profiles.proxy_password, profiles.fingerprint_data, profiles.facebook_logged_in, profiles.facebook_username, profiles.cookies_data, profiles.status, profiles.last_used, profiles.error_message, profiles.created_at, profiles.updated_at 
FROM profiles ORDER BY profiles.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (100, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 15:32:37 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:32:38 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:32:38 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:32:38 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:32:38 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:33:02 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:33:03 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:33:03 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:33:03 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:33:03 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:33:22 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:33:23 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:33:23 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:33:23 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:33:23 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:33:42 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:33:43 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:33:43 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:33:43 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:33:43 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:34:02 | INFO     | __main__:main:24 - Initializing database...
2025-07-10 15:34:02 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:34:02 | INFO     | __main__:main:29 - Database initialization completed successfully!
2025-07-10 15:34:02 | INFO     | __main__:main:34 - Database connection test successful
2025-07-10 15:34:18 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:34:18 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:34:18 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:34:18 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:34:58 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Test Profile
2025-07-10 15:34:58 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Profile_dc800a19
2025-07-10 15:34:58 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: Test Profile
2025-07-10 15:35:59 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Test Workflow Profile
2025-07-10 15:35:59 | ERROR    | app.services.profile_manager:create_profile:88 - Failed to create profile Test Workflow Profile: empty range for randrange() (20, 19, -1)
2025-07-10 15:35:59 | ERROR    | app.core.database:get_db:78 - Database session error: 500: Failed to create profile: empty range for randrange() (20, 19, -1)
2025-07-10 15:36:58 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:36:59 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:36:59 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:36:59 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:36:59 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:37:11 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Test Workflow Profile
2025-07-10 15:37:11 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Workflow Profile_ac9ff127
2025-07-10 15:37:11 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: Test Workflow Profile
2025-07-10 15:37:11 | INFO     | app.api.routes.profiles:update_profile:176 - Profile updated successfully: 2
2025-07-10 15:37:12 | ERROR    | app.services.profile_manager:launch_browser:342 - Browser launch failed: new_context() got an unexpected keyword argument 'user_data_dir'
2025-07-10 15:37:12 | INFO     | app.services.profile_manager:delete_profile:269 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Workflow Profile_ac9ff127
2025-07-10 15:37:12 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 2
2025-07-10 15:37:12 | ERROR    | app.core.database:get_db:78 - Database session error: 404: Profile not found
2025-07-10 15:37:22 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:37:23 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:37:23 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:37:23 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:37:23 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:37:35 | INFO     | app.services.profile_manager:launch_browser:331 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Profile_dc800a19
2025-07-10 15:37:44 | INFO     | app.services.profile_manager:open_facebook_login:362 - Facebook login page opened for profile: Test Profile_dc800a19
2025-07-10 15:37:51 | INFO     | app.services.profile_manager:close_browser:462 - Browser closed for profile: Test Profile_dc800a19
2025-07-10 15:48:21 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 15:48:26 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 15:48:26 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 15:48:26 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 15:48:26 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 15:48:42 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: test
2025-07-10 15:48:42 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a71c6e53
2025-07-10 15:48:42 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: test
2025-07-10 15:48:48 | INFO     | app.services.profile_manager:launch_browser:331 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Profile_dc800a19
2025-07-10 15:49:23 | INFO     | app.services.profile_manager:open_facebook_login:362 - Facebook login page opened for profile: Test Profile_dc800a19
2025-07-10 15:49:43 | INFO     | app.services.profile_manager:close_browser:462 - Browser closed for profile: Test Profile_dc800a19
2025-07-10 15:49:51 | INFO     | app.services.profile_manager:launch_browser:331 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Profile_dc800a19
2025-07-10 15:49:56 | INFO     | app.services.profile_manager:open_facebook_login:362 - Facebook login page opened for profile: Test Profile_dc800a19
2025-07-10 15:50:10 | INFO     | app.services.profile_manager:close_browser:462 - Browser closed for profile: Test Profile_dc800a19
2025-07-10 16:14:33 | INFO     | app.services.profile_manager:launch_browser:331 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a71c6e53
2025-07-10 16:14:36 | INFO     | app.services.profile_manager:open_facebook_login:362 - Facebook login page opened for profile: test_a71c6e53
2025-07-10 16:15:32 | ERROR    | app.services.profile_manager:save_profile_cookies:260 - Failed to save cookies: [Errno 2] No such file or directory: '/tmp/profiles/test_a71c6e53/cookies.json'
2025-07-10 16:15:32 | INFO     | app.services.profile_manager:complete_facebook_login:413 - Facebook login completed for profile: test_a71c6e53
2025-07-10 16:15:36 | INFO     | app.services.profile_manager:test_profile:157 - Testing profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a71c6e53
2025-07-10 16:15:36 | ERROR    | app.services.profile_manager:test_profile:185 - Profile test failed: 'dict' object has no attribute 'user_data_dir'
2025-07-10 16:15:46 | INFO     | app.services.profile_manager:open_facebook_login:362 - Facebook login page opened for profile: test_a71c6e53
2025-07-10 16:15:58 | INFO     | app.services.profile_manager:test_profile:157 - Testing profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a71c6e53
2025-07-10 16:15:58 | ERROR    | app.services.profile_manager:test_profile:185 - Profile test failed: 'dict' object has no attribute 'user_data_dir'
2025-07-10 16:16:11 | INFO     | app.services.profile_manager:test_profile:157 - Testing profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a71c6e53
2025-07-10 16:16:11 | ERROR    | app.services.profile_manager:test_profile:185 - Profile test failed: 'dict' object has no attribute 'user_data_dir'
2025-07-10 16:16:37 | INFO     | app.services.profile_manager:close_browser:462 - Browser closed for profile: test_a71c6e53
2025-07-10 16:16:40 | INFO     | app.services.profile_manager:test_profile:157 - Testing profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a71c6e53
2025-07-10 16:16:40 | ERROR    | app.services.profile_manager:test_profile:185 - Profile test failed: 'dict' object has no attribute 'user_data_dir'
2025-07-10 16:18:15 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:18:16 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:18:16 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:18:16 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:18:16 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:18:44 | INFO     | app.services.profile_manager:test_profile:157 - Testing profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Profile_dc800a19
2025-07-10 16:18:44 | INFO     | app.services.profile_manager:launch_browser:354 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Profile_dc800a19
2025-07-10 16:18:44 | INFO     | app.services.profile_manager:close_browser:485 - Browser closed for profile: Test Profile_dc800a19
2025-07-10 16:19:56 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Test Workflow Profile
2025-07-10 16:19:56 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Workflow Profile_a9fdc7c5
2025-07-10 16:19:56 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: Test Workflow Profile
2025-07-10 16:19:56 | INFO     | app.api.routes.profiles:update_profile:176 - Profile updated successfully: 3
2025-07-10 16:19:56 | INFO     | app.services.profile_manager:test_profile:157 - Testing profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Workflow Profile_a9fdc7c5
2025-07-10 16:19:56 | INFO     | app.services.profile_manager:launch_browser:354 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Workflow Profile_a9fdc7c5
2025-07-10 16:19:56 | INFO     | app.services.profile_manager:close_browser:485 - Browser closed for profile: Test Workflow Profile_a9fdc7c5
2025-07-10 16:19:56 | INFO     | app.services.profile_manager:launch_browser:354 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Workflow Profile_a9fdc7c5
2025-07-10 16:19:57 | INFO     | app.services.profile_manager:open_facebook_login:385 - Facebook login page opened for profile: Test Workflow Profile_a9fdc7c5
2025-07-10 16:19:57 | INFO     | app.services.profile_manager:close_browser:485 - Browser closed for profile: Test Workflow Profile_a9fdc7c5
2025-07-10 16:19:57 | INFO     | app.services.profile_manager:delete_profile:292 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Workflow Profile_a9fdc7c5
2025-07-10 16:19:57 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 3
2025-07-10 16:19:57 | ERROR    | app.core.database:get_db:78 - Database session error: 404: Profile not found
2025-07-10 16:21:12 | INFO     | app.services.profile_manager:test_profile:157 - Testing profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a71c6e53
2025-07-10 16:21:12 | INFO     | app.services.profile_manager:launch_browser:354 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a71c6e53
2025-07-10 16:21:12 | INFO     | app.services.profile_manager:close_browser:485 - Browser closed for profile: test_a71c6e53
2025-07-10 16:21:19 | INFO     | app.services.profile_manager:launch_browser:354 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a71c6e53
2025-07-10 16:21:25 | INFO     | app.services.profile_manager:open_facebook_login:385 - Facebook login page opened for profile: test_a71c6e53
2025-07-10 16:21:40 | INFO     | app.services.profile_manager:delete_profile:292 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a71c6e53
2025-07-10 16:21:40 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 2
2025-07-10 16:21:51 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: test
2025-07-10 16:21:51 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_875c315a
2025-07-10 16:21:51 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: test
2025-07-10 16:21:56 | INFO     | app.services.profile_manager:launch_browser:354 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_875c315a
2025-07-10 16:21:58 | INFO     | app.services.profile_manager:open_facebook_login:385 - Facebook login page opened for profile: test_875c315a
2025-07-10 16:23:39 | INFO     | app.services.profile_manager:close_browser:485 - Browser closed for profile: test_875c315a
2025-07-10 16:23:55 | INFO     | app.services.profile_manager:launch_browser:354 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_875c315a
2025-07-10 16:24:03 | INFO     | app.services.profile_manager:close_browser:485 - Browser closed for profile: test_875c315a
2025-07-10 16:24:10 | INFO     | app.services.profile_manager:launch_browser:354 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_875c315a
2025-07-10 16:24:13 | INFO     | app.services.profile_manager:open_facebook_login:385 - Facebook login page opened for profile: test_875c315a
2025-07-10 16:24:20 | INFO     | app.services.profile_manager:close_browser:485 - Browser closed for profile: test_875c315a
2025-07-10 16:24:50 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:24:54 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:24:54 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:24:54 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:24:54 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:26:24 | INFO     | app.services.profile_manager:launch_browser:354 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_875c315a
2025-07-10 16:26:29 | INFO     | app.services.profile_manager:open_facebook_login:385 - Facebook login page opened for profile: test_875c315a
2025-07-10 16:26:56 | ERROR    | app.services.profile_manager:save_profile_cookies:283 - Failed to save cookies: [Errno 2] No such file or directory: '/tmp/profiles/test_875c315a/cookies.json'
2025-07-10 16:26:56 | INFO     | app.services.profile_manager:complete_facebook_login:436 - Facebook login completed for profile: test_875c315a
2025-07-10 16:26:59 | INFO     | app.services.profile_manager:close_browser:485 - Browser closed for profile: test_875c315a
2025-07-10 16:27:15 | INFO     | app.services.profile_manager:launch_browser:354 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_875c315a
2025-07-10 16:27:17 | INFO     | app.services.profile_manager:open_facebook_login:385 - Facebook login page opened for profile: test_875c315a
2025-07-10 16:28:35 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:28:36 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:28:36 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:28:36 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:28:36 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:28:50 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:28:51 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:28:51 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:28:51 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:28:51 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:29:04 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:29:05 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:29:05 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:29:05 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:29:05 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:29:25 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:29:25 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:29:25 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:29:25 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:29:25 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:30:18 | INFO     | app.services.profile_manager:launch_browser:364 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Profile_dc800a19
2025-07-10 16:30:40 | INFO     | app.services.profile_manager:open_facebook_login:395 - Facebook login page opened for profile: Test Profile_dc800a19
2025-07-10 16:31:03 | INFO     | app.services.profile_manager:close_browser:521 - Browser closed for profile: Test Profile_dc800a19
2025-07-10 16:31:56 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Facebook Test Profile
2025-07-10 16:31:56 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Facebook Test Profile_0ba5c8b7
2025-07-10 16:31:56 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: Facebook Test Profile
2025-07-10 16:31:57 | INFO     | app.services.profile_manager:launch_browser:364 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Facebook Test Profile_0ba5c8b7
2025-07-10 16:31:58 | INFO     | app.services.profile_manager:open_facebook_login:395 - Facebook login page opened for profile: Facebook Test Profile_0ba5c8b7
2025-07-10 16:31:58 | INFO     | app.services.profile_manager:close_browser:521 - Browser closed for profile: Facebook Test Profile_0ba5c8b7
2025-07-10 16:31:58 | INFO     | app.services.profile_manager:launch_browser:364 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Facebook Test Profile_0ba5c8b7
2025-07-10 16:31:58 | INFO     | app.services.profile_manager:close_browser:521 - Browser closed for profile: Facebook Test Profile_0ba5c8b7
2025-07-10 16:31:58 | INFO     | app.services.profile_manager:delete_profile:292 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Facebook Test Profile_0ba5c8b7
2025-07-10 16:31:58 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 3
2025-07-10 16:33:12 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: test2
2025-07-10 16:33:12 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test2_addfded4
2025-07-10 16:33:12 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: test2
2025-07-10 16:33:14 | INFO     | app.services.profile_manager:launch_browser:364 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test2_addfded4
2025-07-10 16:33:17 | INFO     | app.services.profile_manager:open_facebook_login:395 - Facebook login page opened for profile: test2_addfded4
2025-07-10 16:33:52 | ERROR    | app.services.profile_manager:save_profile_cookies:283 - Failed to save cookies: [Errno 2] No such file or directory: '/tmp/profiles/test2_addfded4/cookies.json'
2025-07-10 16:33:52 | INFO     | app.services.profile_manager:complete_facebook_login:446 - Facebook login completed for profile: test2_addfded4
2025-07-10 16:33:52 | ERROR    | app.services.profile_manager:save_profile_cookies:283 - Failed to save cookies: [Errno 2] No such file or directory: '/tmp/profiles/test2_addfded4/cookies.json'
2025-07-10 16:33:52 | INFO     | app.services.profile_manager:complete_facebook_login:446 - Facebook login completed for profile: test2_addfded4
2025-07-10 16:33:57 | INFO     | app.services.profile_manager:open_facebook_login:395 - Facebook login page opened for profile: test2_addfded4
2025-07-10 16:34:07 | INFO     | app.services.profile_manager:test_profile:157 - Testing profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test2_addfded4
2025-07-10 16:34:07 | INFO     | app.services.profile_manager:close_browser:521 - Browser closed for profile: test2_addfded4
2025-07-10 16:34:07 | INFO     | app.services.profile_manager:test_profile:157 - Testing profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test2_addfded4
2025-07-10 16:34:07 | INFO     | app.services.profile_manager:launch_browser:364 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test2_addfded4
2025-07-10 16:34:07 | INFO     | app.services.profile_manager:close_browser:521 - Browser closed for profile: test2_addfded4
2025-07-10 16:34:18 | INFO     | app.services.profile_manager:launch_browser:364 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test2_addfded4
2025-07-10 16:34:21 | INFO     | app.services.profile_manager:open_facebook_login:395 - Facebook login page opened for profile: test2_addfded4
2025-07-10 16:34:30 | INFO     | app.services.profile_manager:close_browser:521 - Browser closed for profile: test2_addfded4
2025-07-10 16:34:45 | INFO     | app.services.profile_manager:launch_browser:364 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test2_addfded4
2025-07-10 16:34:49 | INFO     | app.services.profile_manager:open_facebook_login:395 - Facebook login page opened for profile: test2_addfded4
2025-07-10 16:35:15 | ERROR    | app.services.profile_manager:save_profile_cookies:283 - Failed to save cookies: [Errno 2] No such file or directory: '/tmp/profiles/test2_addfded4/cookies.json'
2025-07-10 16:35:15 | INFO     | app.services.profile_manager:complete_facebook_login:446 - Facebook login completed for profile: test2_addfded4
2025-07-10 16:35:21 | INFO     | app.services.profile_manager:open_facebook_login:395 - Facebook login page opened for profile: test2_addfded4
2025-07-10 16:35:24 | INFO     | app.services.profile_manager:close_browser:521 - Browser closed for profile: test2_addfded4
2025-07-10 16:35:32 | INFO     | app.services.profile_manager:delete_profile:292 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_875c315a
2025-07-10 16:35:32 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 2
2025-07-10 16:35:41 | INFO     | app.services.profile_manager:launch_browser:364 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test2_addfded4
2025-07-10 16:35:44 | INFO     | app.services.profile_manager:open_facebook_login:395 - Facebook login page opened for profile: test2_addfded4
2025-07-10 16:38:57 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:38:58 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:38:58 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:38:58 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:38:58 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:39:24 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:39:24 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:39:24 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:39:24 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:39:24 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:39:36 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:39:36 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:39:36 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:39:36 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:39:36 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:40:00 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:40:01 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:40:01 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:40:01 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:40:01 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:40:13 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Test Auto Launch
2025-07-10 16:40:13 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Auto Launch_08eda986
2025-07-10 16:40:13 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: Test Auto Launch
2025-07-10 16:40:21 | INFO     | app.services.profile_manager:open_facebook_login:411 - Browser not active, auto-launching for profile: Test Auto Launch_08eda986
2025-07-10 16:40:22 | INFO     | app.services.profile_manager:launch_browser:384 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Auto Launch_08eda986
2025-07-10 16:40:23 | INFO     | app.services.profile_manager:open_facebook_login:453 - Facebook opened for profile: Test Auto Launch_08eda986, URL: https://www.facebook.com/
2025-07-10 16:40:37 | INFO     | app.services.profile_manager:close_browser:581 - Browser closed for profile: Test Auto Launch_08eda986
2025-07-10 16:40:44 | INFO     | app.services.profile_manager:open_facebook_login:411 - Browser not active, auto-launching for profile: Test Auto Launch_08eda986
2025-07-10 16:40:45 | INFO     | app.services.profile_manager:launch_browser:384 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Auto Launch_08eda986
2025-07-10 16:40:47 | INFO     | app.services.profile_manager:open_facebook_login:453 - Facebook opened for profile: Test Auto Launch_08eda986, URL: https://www.facebook.com/
2025-07-10 16:41:33 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Auto Launch Test Profile
2025-07-10 16:41:33 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Auto Launch Test Profile_83e5bdf5
2025-07-10 16:41:33 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: Auto Launch Test Profile
2025-07-10 16:41:33 | INFO     | app.services.profile_manager:open_facebook_login:411 - Browser not active, auto-launching for profile: Auto Launch Test Profile_83e5bdf5
2025-07-10 16:41:33 | INFO     | app.services.profile_manager:launch_browser:384 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Auto Launch Test Profile_83e5bdf5
2025-07-10 16:41:35 | INFO     | app.services.profile_manager:open_facebook_login:453 - Facebook opened for profile: Auto Launch Test Profile_83e5bdf5, URL: https://www.facebook.com/
2025-07-10 16:41:35 | INFO     | app.services.profile_manager:close_browser:581 - Browser closed for profile: Auto Launch Test Profile_83e5bdf5
2025-07-10 16:41:35 | INFO     | app.services.profile_manager:open_facebook_login:411 - Browser not active, auto-launching for profile: Auto Launch Test Profile_83e5bdf5
2025-07-10 16:41:36 | INFO     | app.services.profile_manager:launch_browser:384 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Auto Launch Test Profile_83e5bdf5
2025-07-10 16:41:37 | INFO     | app.services.profile_manager:open_facebook_login:453 - Facebook opened for profile: Auto Launch Test Profile_83e5bdf5, URL: https://www.facebook.com/
2025-07-10 16:41:39 | INFO     | app.services.profile_manager:open_facebook_login:453 - Facebook opened for profile: Auto Launch Test Profile_83e5bdf5, URL: https://www.facebook.com/
2025-07-10 16:41:39 | INFO     | app.services.profile_manager:close_browser:581 - Browser closed for profile: Auto Launch Test Profile_83e5bdf5
2025-07-10 16:41:39 | INFO     | app.services.profile_manager:delete_profile:312 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Auto Launch Test Profile_83e5bdf5
2025-07-10 16:41:39 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 5
2025-07-10 16:42:42 | INFO     | app.services.profile_manager:close_browser:581 - Browser closed for profile: Test Auto Launch_08eda986
2025-07-10 16:42:49 | INFO     | app.services.profile_manager:delete_profile:312 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Test Auto Launch_08eda986
2025-07-10 16:42:49 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 4
2025-07-10 16:42:51 | INFO     | app.services.profile_manager:delete_profile:312 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test2_addfded4
2025-07-10 16:42:51 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 3
2025-07-10 16:42:55 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: test
2025-07-10 16:42:55 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a362556f
2025-07-10 16:42:55 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: test
2025-07-10 16:42:59 | INFO     | app.services.profile_manager:launch_browser:384 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a362556f
2025-07-10 16:43:04 | INFO     | app.services.profile_manager:open_facebook_login:453 - Facebook opened for profile: test_a362556f, URL: https://www.facebook.com/
2025-07-10 16:43:28 | ERROR    | app.services.profile_manager:save_facebook_cookies:303 - Failed to save Facebook cookies: [Errno 2] No such file or directory: '/tmp/profiles/test_a362556f/facebook_cookies.json'
2025-07-10 16:43:28 | INFO     | app.services.profile_manager:complete_facebook_login:506 - Facebook login completed for profile: test_a362556f
2025-07-10 16:43:28 | ERROR    | app.services.profile_manager:save_facebook_cookies:303 - Failed to save Facebook cookies: [Errno 2] No such file or directory: '/tmp/profiles/test_a362556f/facebook_cookies.json'
2025-07-10 16:43:28 | INFO     | app.services.profile_manager:complete_facebook_login:506 - Facebook login completed for profile: test_a362556f
2025-07-10 16:43:31 | INFO     | app.services.profile_manager:close_browser:581 - Browser closed for profile: test_a362556f
2025-07-10 16:43:35 | INFO     | app.services.profile_manager:open_facebook_login:411 - Browser not active, auto-launching for profile: test_a362556f
2025-07-10 16:43:35 | INFO     | app.services.profile_manager:launch_browser:384 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a362556f
2025-07-10 16:43:38 | INFO     | app.services.profile_manager:open_facebook_login:453 - Facebook opened for profile: test_a362556f, URL: https://www.facebook.com/
2025-07-10 16:43:50 | INFO     | app.services.profile_manager:open_facebook_login:453 - Facebook opened for profile: test_a362556f, URL: https://www.facebook.com/
2025-07-10 16:44:02 | INFO     | app.services.profile_manager:close_browser:581 - Browser closed for profile: test_a362556f
2025-07-10 16:45:52 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:45:52 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:45:52 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:45:52 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:45:52 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:46:03 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:46:04 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:46:04 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:46:04 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:46:04 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:46:15 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:46:15 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:46:15 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:46:15 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:46:15 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:46:33 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:46:33 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:46:33 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:46:33 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:46:33 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:46:49 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:46:50 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:46:50 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:46:50 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:46:50 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:47:16 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:47:17 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:47:17 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:47:17 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:47:17 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:47:30 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Cookie Test Profile
2025-07-10 16:47:30 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Cookie Test Profile_3e45b4b3
2025-07-10 16:47:30 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: Cookie Test Profile
2025-07-10 16:47:38 | INFO     | app.services.profile_manager:open_facebook_login:440 - Browser not active, auto-launching for profile: Cookie Test Profile_3e45b4b3
2025-07-10 16:47:38 | INFO     | app.services.profile_manager:launch_browser:413 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Cookie Test Profile_3e45b4b3
2025-07-10 16:47:38 | INFO     | app.services.profile_manager:open_facebook_login:479 - No saved cookies found at /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Cookie Test Profile_3e45b4b3/facebook_cookies.json
2025-07-10 16:47:39 | INFO     | app.services.profile_manager:open_facebook_login:539 - No saved cookies, opening login page
2025-07-10 16:47:40 | INFO     | app.services.profile_manager:open_facebook_login:545 - Facebook opened for profile: Cookie Test Profile_3e45b4b3, URL: https://www.facebook.com/login, Logged in: False
2025-07-10 16:48:43 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: Cookie Persistence Test
2025-07-10 16:48:43 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Cookie Persistence Test_ea00a319
2025-07-10 16:48:43 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: Cookie Persistence Test
2025-07-10 16:48:43 | INFO     | app.services.profile_manager:open_facebook_login:440 - Browser not active, auto-launching for profile: Cookie Persistence Test_ea00a319
2025-07-10 16:48:43 | INFO     | app.services.profile_manager:launch_browser:413 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Cookie Persistence Test_ea00a319
2025-07-10 16:48:43 | INFO     | app.services.profile_manager:open_facebook_login:479 - No saved cookies found at /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Cookie Persistence Test_ea00a319/facebook_cookies.json
2025-07-10 16:48:43 | INFO     | app.services.profile_manager:open_facebook_login:539 - No saved cookies, opening login page
2025-07-10 16:48:45 | INFO     | app.services.profile_manager:open_facebook_login:545 - Facebook opened for profile: Cookie Persistence Test_ea00a319, URL: https://www.facebook.com/login, Logged in: False
2025-07-10 16:48:45 | INFO     | app.services.profile_manager:close_browser:675 - Browser closed for profile: Cookie Persistence Test_ea00a319
2025-07-10 16:48:45 | INFO     | app.services.profile_manager:open_facebook_login:440 - Browser not active, auto-launching for profile: Cookie Persistence Test_ea00a319
2025-07-10 16:48:45 | INFO     | app.services.profile_manager:launch_browser:413 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Cookie Persistence Test_ea00a319
2025-07-10 16:48:45 | INFO     | app.services.profile_manager:open_facebook_login:479 - No saved cookies found at /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Cookie Persistence Test_ea00a319/facebook_cookies.json
2025-07-10 16:48:45 | INFO     | app.services.profile_manager:open_facebook_login:539 - No saved cookies, opening login page
2025-07-10 16:48:47 | INFO     | app.services.profile_manager:open_facebook_login:545 - Facebook opened for profile: Cookie Persistence Test_ea00a319, URL: https://www.facebook.com/login, Logged in: False
2025-07-10 16:48:47 | INFO     | app.services.profile_manager:close_browser:675 - Browser closed for profile: Cookie Persistence Test_ea00a319
2025-07-10 16:48:47 | INFO     | app.services.profile_manager:delete_profile:341 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Cookie Persistence Test_ea00a319
2025-07-10 16:48:47 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 4
2025-07-10 16:49:22 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:49:23 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:49:23 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:49:23 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:49:23 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:50:00 | INFO     | app.services.profile_manager:open_facebook_login:440 - Browser not active, auto-launching for profile: Cookie Test Profile_3e45b4b3
2025-07-10 16:50:01 | INFO     | app.services.profile_manager:launch_browser:413 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Cookie Test Profile_3e45b4b3
2025-07-10 16:50:01 | INFO     | app.services.profile_manager:open_facebook_login:471 - Loaded 4 Facebook cookies for profile: Cookie Test Profile_3e45b4b3
2025-07-10 16:50:01 | INFO     | app.services.profile_manager:open_facebook_login:485 - Attempting to use saved cookies for auto-login
2025-07-10 16:50:05 | INFO     | app.services.profile_manager:open_facebook_login:496 - After loading with cookies, URL: https://www.facebook.com/
2025-07-10 16:50:10 | WARNING  | app.services.profile_manager:open_facebook_login:525 - Error checking login status: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("div[role=\"banner\"], div[data-testid=\"royal_login_form\"]") to be visible

2025-07-10 16:50:10 | INFO     | app.services.profile_manager:open_facebook_login:545 - Facebook opened for profile: Cookie Test Profile_3e45b4b3, URL: https://www.facebook.com/login, Logged in: False
2025-07-10 16:51:05 | INFO     | app.services.profile_manager:delete_profile:341 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/Cookie Test Profile_3e45b4b3
2025-07-10 16:51:05 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 3
2025-07-10 16:51:59 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
2025-07-10 16:52:05 | INFO     | main:lifespan:34 - Starting Facebook Automation Backend...
2025-07-10 16:52:05 | INFO     | app.core.database:init_db:64 - Database initialized successfully
2025-07-10 16:52:05 | INFO     | main:lifespan:38 - Database initialized
2025-07-10 16:52:05 | INFO     | main:lifespan:50 - Crawl4ai initialization skipped for development
2025-07-10 16:52:40 | INFO     | app.services.profile_manager:delete_profile:341 - Profile deleted: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_a362556f
2025-07-10 16:52:40 | INFO     | app.api.routes.profiles:delete_profile:211 - Profile deleted successfully: 2
2025-07-10 16:52:45 | INFO     | app.services.profile_manager:create_profile:49 - Creating profile: test
2025-07-10 16:52:45 | INFO     | app.services.profile_manager:create_profile:78 - Profile created successfully: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_eadfcc2c
2025-07-10 16:52:45 | INFO     | app.api.routes.profiles:create_profile:120 - Profile created successfully: test
2025-07-10 16:52:49 | INFO     | app.services.profile_manager:launch_browser:413 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_eadfcc2c
2025-07-10 16:52:50 | INFO     | app.services.profile_manager:open_facebook_login:479 - No saved cookies found at /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_eadfcc2c/facebook_cookies.json
2025-07-10 16:52:50 | INFO     | app.services.profile_manager:open_facebook_login:539 - No saved cookies, opening login page
2025-07-10 16:52:52 | INFO     | app.services.profile_manager:open_facebook_login:545 - Facebook opened for profile: test_eadfcc2c, URL: https://www.facebook.com/login, Logged in: False
2025-07-10 16:53:28 | INFO     | app.services.profile_manager:save_facebook_cookies:323 - Saved 9 Facebook cookies to /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_eadfcc2c/facebook_cookies.json
2025-07-10 16:53:28 | INFO     | app.services.profile_manager:complete_facebook_login:600 - Facebook login completed for profile: test_eadfcc2c
2025-07-10 16:53:30 | INFO     | app.services.profile_manager:close_browser:675 - Browser closed for profile: test_eadfcc2c
2025-07-10 16:53:32 | INFO     | app.services.profile_manager:open_facebook_login:440 - Browser not active, auto-launching for profile: test_eadfcc2c
2025-07-10 16:53:33 | INFO     | app.services.profile_manager:launch_browser:413 - Browser launched successfully for profile: /Users/<USER>/Documents/Projects/antidetect-browser/backend/data/profiles/test_eadfcc2c
2025-07-10 16:53:33 | INFO     | app.services.profile_manager:open_facebook_login:471 - Loaded 9 Facebook cookies for profile: test_eadfcc2c
2025-07-10 16:53:33 | INFO     | app.services.profile_manager:open_facebook_login:485 - Attempting to use saved cookies for auto-login
2025-07-10 16:53:40 | WARNING  | app.services.profile_manager:open_facebook_login:532 - Error loading Facebook with cookies: Target page, context or browser has been closed
2025-07-10 16:53:40 | ERROR    | app.services.profile_manager:open_facebook_login:557 - Failed to open Facebook: Page.goto: Target page, context or browser has been closed
2025-07-10 16:53:48 | INFO     | app.services.profile_manager:open_facebook_login:471 - Loaded 9 Facebook cookies for profile: test_eadfcc2c
2025-07-10 16:53:48 | INFO     | app.services.profile_manager:open_facebook_login:485 - Attempting to use saved cookies for auto-login
2025-07-10 16:54:01 | WARNING  | app.services.profile_manager:open_facebook_login:532 - Error loading Facebook with cookies: Target page, context or browser has been closed
2025-07-10 16:54:01 | ERROR    | app.services.profile_manager:open_facebook_login:557 - Failed to open Facebook: Page.goto: Target page, context or browser has been closed
2025-07-10 16:54:08 | INFO     | app.services.profile_manager:open_facebook_login:471 - Loaded 9 Facebook cookies for profile: test_eadfcc2c
2025-07-10 16:54:08 | INFO     | app.services.profile_manager:open_facebook_login:485 - Attempting to use saved cookies for auto-login
2025-07-10 16:54:12 | WARNING  | app.services.profile_manager:open_facebook_login:532 - Error loading Facebook with cookies: Target page, context or browser has been closed
2025-07-10 16:54:12 | ERROR    | app.services.profile_manager:open_facebook_login:557 - Failed to open Facebook: Page.goto: Target page, context or browser has been closed
2025-07-10 16:54:23 | INFO     | main:lifespan:55 - Shutting down Facebook Automation Backend...
